-------------------------------------------------------------------------------
Test set: com.example.encryption.DynamicStrategyAndSM2Test
-------------------------------------------------------------------------------
Tests run: 8, Failures: 2, Errors: 2, Skipped: 0, Time elapsed: 9.66 s <<< FAILURE! - in com.example.encryption.DynamicStrategyAndSM2Test
testEncryptionUtilWithDifferentAlgorithms  Time elapsed: 1.203 s  <<< ERROR!
java.lang.RuntimeException: 加密失败: SM2加密失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
Caused by: java.lang.RuntimeException: SM2加密失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
Caused by: java.lang.RuntimeException: 解析SM2公钥失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
Caused by: org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)

testDynamicStrategyService  Time elapsed: 0.066 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <SM2> but was: <AES>
	at com.example.encryption.DynamicStrategyAndSM2Test.testDynamicStrategyService(DynamicStrategyAndSM2Test.java:91)

testSM2Encryption  Time elapsed: 0.485 s  <<< ERROR!
java.lang.RuntimeException: SM2加密失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37)
Caused by: java.lang.RuntimeException: 解析SM2公钥失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37)
Caused by: org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37)

testStrategyStatistics  Time elapsed: 0.07 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <true> but was: <false>
	at com.example.encryption.DynamicStrategyAndSM2Test.testStrategyStatistics(DynamicStrategyAndSM2Test.java:218)

