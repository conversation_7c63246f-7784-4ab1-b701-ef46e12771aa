spring:
  datasource:
    url: jdbc:h2:mem:testdb-test
    driver-class-name: org.h2.Driver
    username: sa
    password: password
    initialization-mode: always
    schema: classpath:schema.sql

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.H2Dialect

  h2:
    console:
      enabled: false

logging:
  level:
    com.example.encryption: INFO
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN

# 测试环境的加密配置
encryption:
  secret-key: "TestSecretKey123456789012345678"
