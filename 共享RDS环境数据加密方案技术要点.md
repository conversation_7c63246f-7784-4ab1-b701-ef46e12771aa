# 共享RDS环境数据加密方案技术要点

## 统一技术栈设计

### 核心设计原则
- **统一加密组件**：所有方案都使用相同的@EncryptField注解和EncryptionUtil工具类
- **ORM框架支持**：同时支持JPA和MyBatis两种ORM框架
- **避免存储过程**：全部使用Java应用层处理，便于维护和调试
- **配置驱动**：通过注解和配置文件管理加密策略

### 统一加密注解
```java
@EncryptField(
    description = "患者姓名",
    migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT, // 方案一、二使用
    strategyKey = "name"
)
@Convert(converter = EncryptConverter.class) // JPA方式
// 或者在MyBatis中使用 typeHandler=EncryptTypeHandler
private String name;
```

## 环境约束条件

### 1. 共享RDS实例限制
- **资源共享**：与其他数据库共用同一RDS实例
- **并发限制**：不能产生高并发，影响其他业务
- **资源竞争**：CPU、内存、连接数等资源有限
- **性能影响**：需要控制对整体性能的影响

### 2. 长事务限制
- **锁表风险**：长事务可能导致表锁，影响其他业务
- **资源占用**：长事务占用连接和内存资源
- **回滚风险**：长事务回滚代价高
- **死锁风险**：增加死锁概率

### 3. 字段扩长要求
- **加密长度**：AES-256-GCM加密后长度显著增加
- **存储影响**：需要扩展字段长度以适应密文
- **索引影响**：字段长度变化可能影响索引性能
- **兼容性**：需要考虑应用程序的兼容性

## 三种方案的技术适应性

### 方案一：备份数据库增量迁移

#### 优势
```java
// 1. 资源隔离 - 备份数据库独立处理
@Configuration
public class BackupDataSourceConfig {
    @Bean
    @ConfigurationProperties("backup.datasource")
    public DataSource backupDataSource() {
        return DataSourceBuilder.create().build();
    }

    @Bean
    public JdbcTemplate backupJdbcTemplate(@Qualifier("backupDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }
}

// 2. 分批处理避免长事务
public void encryptDataInBatches() {
    int batchSize = 100; // 小批次
    int processedCount = 0;

    while (processedCount < totalCount) {
        // 每批独立事务
        List<Object[]> batch = processBatch(batchSize, processedCount);
        backupJdbcTemplate.batchUpdate(sql, batch);

        // 控制处理速度
        Thread.sleep(100);
        processedCount += batchSize;
    }
}
```

#### 劣势
- **额外RDS实例**：需要创建备份数据库，增加资源成本
- **网络传输**：数据库间传输消耗网络带宽
- **数据同步**：增量数据识别和同步复杂

### 方案二：新表替换

#### 优势
```java
// 1. 分表分批处理，避免同时锁定多个表
public void migrateTablesSequentially() {
    // 按表顺序处理
    migratePatientTable();
    Thread.sleep(5000); // 表间间隔

    migrateUserTable();
    Thread.sleep(5000);

    // 其他表...
}

// 2. 小批次独立事务
@Transactional(propagation = Propagation.REQUIRES_NEW)
public void migrateBatch(List<Map<String, Object>> batch) {
    String insertSql = "INSERT INTO ych_patient_new (id, name, phone, ...) VALUES (?, ?, ?, ...)";

    List<Object[]> batchArgs = new ArrayList<>();
    for (Map<String, Object> row : batch) {
        Object[] args = new Object[]{
            row.get("id"),
            encryptionUtil.encrypt((String) row.get("name")),
            encryptionUtil.encrypt((String) row.get("phone")),
            // ...
        };
        batchArgs.add(args);
    }

    jdbcTemplate.batchUpdate(insertSql, batchArgs);
}

// 3. 增量数据精确处理
public void handleIncrementalData(LocalDateTime startTime) {
    // 时间戳 + UPSERT 处理增量
    String upsertSql = "INSERT INTO ych_patient_new (...) VALUES (...) " +
                      "ON DUPLICATE KEY UPDATE name=VALUES(name), ...";

    List<Map<String, Object>> incrementalData = jdbcTemplate.queryForList(
        "SELECT * FROM ych_patient WHERE updated_at >= ?", startTime);

    // 分批UPSERT
    processBatchUpsert(incrementalData, upsertSql);
}
```

#### 劣势
- **双倍存储**：临时需要双倍存储空间
- **表重命名风险**：原子操作期间的短暂锁表

### 方案三：渐进式影子字段（推荐）

#### 优势
```java
// 1. JPA自动事务管理，单条记录事务
@Entity
public class Patient {
    @EncryptField(
        shadowField = "name_encrypted",
        migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY,
        strategyKey = "name"
    )
    private String name;

    @Column(name = "name_encrypted")
    private String nameEncrypted;
}

// 2. 配置驱动的并发控制
@Component
@ConfigurationProperties(prefix = "encryption.migration")
public class MigrationStrategyConfig {
    private MigrationStrategy defaultStrategy = MigrationStrategy.PLAINTEXT_PRIORITY;
    private boolean enableGlobalOverride = false;
    private Map<String, MigrationStrategy> strategies = new HashMap<>();

    // 运行时动态调整策略，无需重启
    public void updateStrategy(String key, MigrationStrategy strategy) {
        strategies.put(key, strategy);
    }
}

// 3. 渐进式批量迁移，可控并发
@Service
public class MigrationService {
    public MigrationResult migrateToVersion(int targetVersion) {
        // 分页处理，控制并发
        int pageSize = 50; // 小批次
        int pageNum = 0;

        while (true) {
            Page<MigrationUser> page = userRepository.findByDataVersionLessThan(
                targetVersion, PageRequest.of(pageNum, pageSize));

            if (page.isEmpty()) break;

            // 批量更新
            for (MigrationUser user : page.getContent()) {
                migrationManager.migrateUserToVersion(user, targetVersion);
            }

            // 控制处理速度
            Thread.sleep(100);
            pageNum++;
        }
    }
}
```

#### 技术优势
- **零长事务**：JPA单条记录事务，完全避免长事务
- **最低并发**：渐进式处理，对共享RDS影响最小
- **动态控制**：运行时调整策略，无需重启应用
- **资源友好**：内存占用稳定，CPU消耗均匀分布

## 关键技术实现要点

### 1. 并发控制策略

```java
// 方案一：备份库独立处理
@Component
public class BackupMigrationController {
    @Value("${migration.batch.size:100}")
    private int batchSize;

    @Value("${migration.batch.interval:100}")
    private int batchInterval; // 毫秒

    public void controlledMigration() {
        // 小批次 + 间隔控制
        processBatch(batchSize);
        Thread.sleep(batchInterval);
    }
}

// 方案二：表级别控制
@Component
public class TableMigrationController {
    @Value("${migration.table.interval:5000}")
    private int tableInterval; // 表间间隔

    public void migrateAllTables() {
        for (String tableName : tableNames) {
            migrateTable(tableName);
            Thread.sleep(tableInterval); // 表间休息
        }
    }
}

// 方案三：应用级别控制
@Component
public class ApplicationMigrationController {
    @Value("${migration.page.size:50}")
    private int pageSize;

    @Value("${migration.record.interval:10}")
    private int recordInterval;

    public void gradualMigration() {
        // 最小粒度控制
        processPage(pageSize);
        Thread.sleep(recordInterval);
    }
}
```

### 2. 事务控制策略

```java
// 方案一：手动事务控制
@Service
public class BackupMigrationService {
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void migrateBatch(List<Data> batch) {
        // 每批独立事务，避免长事务
        for (Data data : batch) {
            processData(data);
        }
    }
}

// 方案二：批量事务控制
@Service
public class NewTableMigrationService {
    @Transactional(propagation = Propagation.REQUIRES_NEW, timeout = 30)
    public void migrateBatchWithTimeout(List<Data> batch) {
        // 设置事务超时，防止长事务
        batchUpdate(batch);
    }
}

// 方案三：JPA自动事务（推荐）
@Service
public class ShadowFieldMigrationService {
    @Transactional // JPA自动管理，单条记录事务
    public void migrateUser(MigrationUser user) {
        // 自动事务管理，最小事务粒度
        userRepository.save(user);
    }
}
```

### 3. 增量数据识别策略

```java
// 精确增量识别工具类
@Component
public class IncrementalDataIdentifier {

    /**
     * 基于时间戳 + 主键范围的双重识别
     */
    public List<Long> identifyIncrementalIds(String tableName, LocalDateTime startTime) {
        // 1. 获取时间戳范围
        String timeSql = "SELECT id FROM " + tableName + " WHERE updated_at >= ?";
        List<Long> timeBasedIds = jdbcTemplate.queryForList(timeSql, Long.class, startTime);

        // 2. 获取ID范围（防止时间戳不准确）
        String maxIdSql = "SELECT COALESCE(MAX(id), 0) FROM " + tableName + "_backup";
        Long maxProcessedId = jdbcTemplate.queryForObject(maxIdSql, Long.class);

        String idSql = "SELECT id FROM " + tableName + " WHERE id > ?";
        List<Long> idBasedIds = jdbcTemplate.queryForList(idSql, Long.class, maxProcessedId);

        // 3. 合并去重
        Set<Long> incrementalIds = new HashSet<>();
        incrementalIds.addAll(timeBasedIds);
        incrementalIds.addAll(idBasedIds);

        return new ArrayList<>(incrementalIds);
    }

    /**
     * 多表增量数据协调处理
     */
    public Map<String, List<Long>> identifyMultiTableIncremental(
            List<String> tableNames, LocalDateTime startTime) {

        Map<String, List<Long>> result = new HashMap<>();

        for (String tableName : tableNames) {
            List<Long> ids = identifyIncrementalIds(tableName, startTime);
            result.put(tableName, ids);

            // 表间处理间隔
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        return result;
    }
}
```

## 推荐实施方案

基于共享RDS环境的约束条件，**强烈推荐方案三（渐进式影子字段方案）**：

### 核心优势
1. **对共享RDS最友好**：单条事务，渐进式处理
2. **完全避免长事务**：JPA自动管理，事务粒度最小
3. **并发控制最优**：可动态调整处理速度
4. **资源消耗最稳定**：内存和CPU消耗均匀分布
5. **运维最简单**：配置驱动，无需复杂的数据库操作

### 实施要点
- 使用小批次分页处理（50条/页）
- 设置合理的处理间隔（10-100ms）
- 通过配置文件动态调整策略
- 实时监控RDS资源使用情况
- 准备快速回滚机制

这种方案最适合共享RDS环境，能够在保证数据安全的前提下，最小化对其他业务的影响。
