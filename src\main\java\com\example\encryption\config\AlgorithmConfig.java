package com.example.encryption.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 加密算法配置类
 * 从配置文件中读取各种加密算法的配置信息
 */
@Component
@ConfigurationProperties(prefix = "encryption.algorithms")
public class AlgorithmConfig {

    /**
     * 默认加密算法
     */
    private String defaultAlgorithm = "AES";

    /**
     * AES算法配置
     */
    private AESConfig aes = new AESConfig();

    /**
     * SM2算法配置
     */
    private SM2Config sm2 = new SM2Config();

    /**
     * SM4算法配置
     */
    private SM4Config sm4 = new SM4Config();

    // Getters and Setters
    public String getDefaultAlgorithm() {
        return defaultAlgorithm;
    }

    public void setDefaultAlgorithm(String defaultAlgorithm) {
        this.defaultAlgorithm = defaultAlgorithm;
    }

    public AESConfig getAes() {
        return aes;
    }

    public void setAes(AESConfig aes) {
        this.aes = aes;
    }

    public SM2Config getSm2() {
        return sm2;
    }

    public void setSm2(SM2Config sm2) {
        this.sm2 = sm2;
    }

    public SM4Config getSm4() {
        return sm4;
    }

    public void setSm4(SM4Config sm4) {
        this.sm4 = sm4;
    }

    /**
     * 获取算法配置信息
     * 
     * @param algorithmName 算法名称
     * @return 算法配置信息
     */
    public Map<String, Object> getAlgorithmInfo(String algorithmName) {
        Map<String, Object> info = new HashMap<>();
        
        switch (algorithmName.toUpperCase()) {
            case "AES":
                info.put("enabled", aes.isEnabled());
                info.put("keySize", aes.getKeySize());
                info.put("mode", aes.getMode());
                info.put("description", aes.getDescription());
                break;
            case "SM2":
                info.put("enabled", sm2.isEnabled());
                info.put("description", sm2.getDescription());
                info.put("hasKeys", sm2.getPublicKey() != null && sm2.getPrivateKey() != null);
                break;
            case "SM4":
                info.put("enabled", sm4.isEnabled());
                info.put("description", sm4.getDescription());
                info.put("note", sm4.getNote());
                break;
            default:
                info.put("enabled", false);
                info.put("description", "未知算法");
        }
        
        return info;
    }

    /**
     * 检查算法是否启用
     * 
     * @param algorithmName 算法名称
     * @return 是否启用
     */
    public boolean isAlgorithmEnabled(String algorithmName) {
        switch (algorithmName.toUpperCase()) {
            case "AES":
                return aes.isEnabled();
            case "SM2":
                return sm2.isEnabled();
            case "SM4":
                return sm4.isEnabled();
            default:
                return false;
        }
    }

    /**
     * AES算法配置
     */
    public static class AESConfig {
        private boolean enabled = true;
        private int keySize = 256;
        private String mode = "GCM";
        private String description = "高级加密标准，性能优秀，广泛使用";

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public int getKeySize() { return keySize; }
        public void setKeySize(int keySize) { this.keySize = keySize; }

        public String getMode() { return mode; }
        public void setMode(String mode) { this.mode = mode; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }

    /**
     * SM2算法配置
     */
    public static class SM2Config {
        private boolean enabled = true;
        private String description = "国密椭圆曲线公钥密码算法，符合国家安全要求";
        private String publicKey;
        private String privateKey;

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getPublicKey() { return publicKey; }
        public void setPublicKey(String publicKey) { this.publicKey = publicKey; }

        public String getPrivateKey() { return privateKey; }
        public void setPrivateKey(String privateKey) { this.privateKey = privateKey; }
    }

    /**
     * SM4算法配置
     */
    public static class SM4Config {
        private boolean enabled = false;
        private String description = "国密对称加密算法，开发中";
        private String note = "SM4算法实现正在开发中";

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getNote() { return note; }
        public void setNote(String note) { this.note = note; }
    }

    /**
     * 获取所有算法的配置摘要
     * 
     * @return 配置摘要
     */
    public Map<String, Object> getAllAlgorithmsInfo() {
        Map<String, Object> allInfo = new HashMap<>();
        allInfo.put("default", defaultAlgorithm);
        allInfo.put("AES", getAlgorithmInfo("AES"));
        allInfo.put("SM2", getAlgorithmInfo("SM2"));
        allInfo.put("SM4", getAlgorithmInfo("SM4"));
        return allInfo;
    }

    /**
     * 验证配置是否有效
     * 
     * @return 验证结果
     */
    public Map<String, String> validateConfig() {
        Map<String, String> issues = new HashMap<>();
        
        // 检查默认算法是否启用
        if (!isAlgorithmEnabled(defaultAlgorithm)) {
            issues.put("defaultAlgorithm", "默认算法 " + defaultAlgorithm + " 未启用");
        }
        
        // 检查SM2密钥配置
        if (sm2.isEnabled()) {
            if (sm2.getPublicKey() == null || sm2.getPublicKey().trim().isEmpty()) {
                issues.put("sm2.publicKey", "SM2算法已启用但未配置公钥");
            }
            if (sm2.getPrivateKey() == null || sm2.getPrivateKey().trim().isEmpty()) {
                issues.put("sm2.privateKey", "SM2算法已启用但未配置私钥");
            }
        }
        
        return issues;
    }

    @Override
    public String toString() {
        return String.format("AlgorithmConfig{default='%s', aes.enabled=%s, sm2.enabled=%s, sm4.enabled=%s}", 
                           defaultAlgorithm, aes.isEnabled(), sm2.isEnabled(), sm4.isEnabled());
    }
}
