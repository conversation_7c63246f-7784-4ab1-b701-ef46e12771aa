<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据加密迁移管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/theme-chalk/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        .header p {
            margin: 5px 0 0 0;
            opacity: 0.8;
        }
        .container {
            max-width: 1400px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .card-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #ebeef5;
            font-weight: 600;
            color: #303133;
        }
        .card-body {
            padding: 20px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .status-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #409eff;
        }
        .status-card.success {
            border-left-color: #67c23a;
        }
        .status-card.warning {
            border-left-color: #e6a23c;
        }
        .status-card.danger {
            border-left-color: #f56c6c;
        }
        .status-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #303133;
        }
        .status-value {
            font-size: 24px;
            font-weight: 700;
            color: #409eff;
        }
        .progress-container {
            margin: 15px 0;
        }
        .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
            color: #606266;
        }
        .operation-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-timestamp {
            color: #a0aec0;
        }
        .log-level-info {
            color: #63b3ed;
        }
        .log-level-success {
            color: #68d391;
        }
        .log-level-warning {
            color: #fbd38d;
        }
        .log-level-error {
            color: #fc8181;
        }
        .table-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        .table-card {
            border: 1px solid #ebeef5;
            border-radius: 6px;
            padding: 15px;
            background: #fafafa;
        }
        .table-name {
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
        }
        .auto-refresh {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> 数据加密迁移管理平台</h1>
            <p>肿瘤医院公众后台安全整改专项管理系统</p>
        </div>

        <div class="container">
            <!-- 系统状态概览 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-tachometer-alt"></i> 系统状态概览
                    <div class="auto-refresh" style="float: right;">
                        <el-switch v-model="autoRefresh" active-text="自动刷新"></el-switch>
                        <el-button size="mini" @click="refreshAll" :loading="loading">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </el-button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="status-grid">
                        <div class="status-card" :class="getStatusClass(systemStatus.migrationStatus)">
                            <div class="status-title">
                                <i class="fas fa-database"></i> 迁移状态
                            </div>
                            <div class="status-value">{{ systemStatus.migrationStatus }}</div>
                            <div style="margin-top: 10px; font-size: 14px; color: #606266;">
                                总进度: {{ systemStatus.overallProgress }}%
                            </div>
                        </div>
                        
                        <div class="status-card" :class="getStatusClass(systemStatus.cleanupStatus)">
                            <div class="status-title">
                                <i class="fas fa-broom"></i> 清理状态
                            </div>
                            <div class="status-value">{{ systemStatus.cleanupStatus }}</div>
                            <div style="margin-top: 10px; font-size: 14px; color: #606266;">
                                清理进度: {{ systemStatus.cleanupProgress }}%
                            </div>
                        </div>
                        
                        <div class="status-card success">
                            <div class="status-title">
                                <i class="fas fa-lock"></i> 加密字段数
                            </div>
                            <div class="status-value">{{ systemStatus.encryptedFields }}</div>
                        </div>
                        
                        <div class="status-card" :class="systemStatus.activeJobs > 0 ? 'warning' : 'success'">
                            <div class="status-title">
                                <i class="fas fa-tasks"></i> 活跃任务
                            </div>
                            <div class="status-value">{{ systemStatus.activeJobs }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 迁移策略管理 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-cogs"></i> 迁移策略管理
                </div>
                <div class="card-body">
                    <div class="operation-buttons">
                        <el-button type="primary" @click="switchStrategy('PLAINTEXT_PRIORITY')" :loading="strategyLoading">
                            <i class="fas fa-file-alt"></i> 明文优先
                        </el-button>
                        <el-button type="warning" @click="switchStrategy('SHADOW_PRIORITY')" :loading="strategyLoading">
                            <i class="fas fa-exchange-alt"></i> 影子字段优先
                        </el-button>
                        <el-button type="success" @click="switchStrategy('SHADOW_ONLY')" :loading="strategyLoading">
                            <i class="fas fa-shield-alt"></i> 仅影子字段
                        </el-button>
                        <el-button type="danger" @click="rollbackStrategy" :loading="strategyLoading">
                            <i class="fas fa-undo"></i> 紧急回滚
                        </el-button>
                    </div>
                    
                    <el-alert 
                        :title="'当前策略: ' + currentStrategy" 
                        :type="getAlertType(currentStrategy)"
                        :closable="false"
                        show-icon>
                    </el-alert>
                </div>
            </div>

            <!-- 数据迁移管理 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-database"></i> 数据迁移管理
                </div>
                <div class="card-body">
                    <div class="operation-buttons">
                        <el-button type="primary" @click="startMigration(2)" :loading="migrationLoading">
                            <i class="fas fa-play"></i> 开始迁移到版本2
                        </el-button>
                        <el-button type="info" @click="validateMigration" :loading="migrationLoading">
                            <i class="fas fa-check-circle"></i> 验证迁移
                        </el-button>
                        <el-button @click="getMigrationStats">
                            <i class="fas fa-chart-bar"></i> 查看统计
                        </el-button>
                    </div>
                    
                    <div class="table-status">
                        <div v-for="table in migrationProgress" :key="table.tableName" class="table-card">
                            <div class="table-name">{{ table.tableName }}</div>
                            <div class="progress-container">
                                <div class="progress-info">
                                    <span>{{ table.processedCount }} / {{ table.totalCount }}</span>
                                    <span>{{ table.progressPercent }}%</span>
                                </div>
                                <el-progress 
                                    :percentage="table.progressPercent" 
                                    :status="getProgressStatus(table.status)">
                                </el-progress>
                            </div>
                            <div style="font-size: 12px; color: #909399;">
                                状态: {{ table.status }} | 最后更新: {{ formatTime(table.lastUpdateTime) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 明文字段清理管理 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-broom"></i> 明文字段清理管理
                </div>
                <div class="card-body">
                    <div class="operation-buttons">
                        <el-button type="success" @click="startCleanup('ych_patient')" :loading="cleanupLoading">
                            <i class="fas fa-play"></i> 开始清理患者表
                        </el-button>
                        <el-button type="success" @click="startCleanup('ych_user')" :loading="cleanupLoading">
                            <i class="fas fa-play"></i> 开始清理用户表
                        </el-button>
                        <el-button type="success" @click="startCleanup('ych_appoint_record')" :loading="cleanupLoading">
                            <i class="fas fa-play"></i> 开始清理预约表
                        </el-button>
                        <el-button type="warning" @click="pauseAllCleanup" :loading="cleanupLoading">
                            <i class="fas fa-pause"></i> 暂停所有清理
                        </el-button>
                        <el-button type="info" @click="resumeAllCleanup" :loading="cleanupLoading">
                            <i class="fas fa-play"></i> 恢复所有清理
                        </el-button>
                    </div>
                    
                    <div class="table-status">
                        <div v-for="cleanup in cleanupProgress" :key="cleanup.tableName" class="table-card">
                            <div class="table-name">{{ cleanup.tableName }}</div>
                            <div class="progress-container">
                                <div class="progress-info">
                                    <span>{{ cleanup.processedCount }} / {{ cleanup.totalCount }}</span>
                                    <span>{{ cleanup.progressPercent.toFixed(1) }}%</span>
                                </div>
                                <el-progress 
                                    :percentage="cleanup.progressPercent" 
                                    :status="getProgressStatus(cleanup.status)">
                                </el-progress>
                            </div>
                            <div style="font-size: 12px; color: #909399; margin-top: 10px;">
                                状态: {{ cleanup.status }}
                                <span v-if="cleanup.lastProcessedId"> | 最后处理ID: {{ cleanup.lastProcessedId }}</span>
                            </div>
                            <div style="margin-top: 10px;">
                                <el-button size="mini" @click="pauseCleanup(cleanup.tableName)" v-if="cleanup.status === 'RUNNING'">
                                    <i class="fas fa-pause"></i> 暂停
                                </el-button>
                                <el-button size="mini" @click="resumeCleanup(cleanup.tableName)" v-if="cleanup.status === 'PAUSED'">
                                    <i class="fas fa-play"></i> 恢复
                                </el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作日志 -->
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-list-alt"></i> 操作日志
                    <el-button size="mini" style="float: right;" @click="clearLogs">
                        <i class="fas fa-trash"></i> 清空日志
                    </el-button>
                </div>
                <div class="card-body">
                    <div class="log-container" ref="logContainer">
                        <div v-for="log in logs" :key="log.id" class="log-entry">
                            <span class="log-timestamp">{{ log.timestamp }}</span>
                            <span :class="'log-level-' + log.level">[{{ log.level.toUpperCase() }}]</span>
                            <span>{{ log.message }}</span>
                        </div>
                        <div v-if="logs.length === 0" style="text-align: center; color: #909399; padding: 20px;">
                            暂无日志记录
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入Vue和Element UI -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.14/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.24.0/dist/axios.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
