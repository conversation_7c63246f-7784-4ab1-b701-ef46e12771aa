package com.example.encryption.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * 统一加密工具类
 * 支持多种加密算法，根据配置动态选择加密方式
 */
public class EncryptionUtil {

    private static final Logger logger = LoggerFactory.getLogger(EncryptionUtil.class);

    /**
     * 支持的加密算法类型
     */
    public enum AlgorithmType {
        AES("AES-GCM", "高级加密标准，对称加密算法"),
        SM2("SM2", "国密椭圆曲线公钥密码算法"),
        SM4("SM4", "国密对称加密算法（暂未实现）");

        private final String displayName;
        private final String description;

        AlgorithmType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 根据算法类型加密数据
     * 
     * @param plaintext 明文
     * @param algorithmType 算法类型
     * @return 加密后的密文
     */
    public static String encrypt(String plaintext, AlgorithmType algorithmType) {
        if (!StringUtils.hasText(plaintext)) {
            return plaintext;
        }

        try {
            logger.debug("使用{}算法加密数据，明文长度: {}", algorithmType.getDisplayName(), plaintext.length());

            String result;
            switch (algorithmType) {
                case AES:
                    result = AESGCMUtil.encrypt(plaintext);
                    break;
                case SM2:
                    result = SM2Util.encrypt(plaintext);
                    break;
                case SM4:
                    // TODO: 实现SM4算法
                    throw new UnsupportedOperationException("SM4算法暂未实现");
                default:
                    throw new IllegalArgumentException("不支持的加密算法: " + algorithmType);
            }

            logger.debug("{}算法加密成功，密文长度: {}", algorithmType.getDisplayName(), result.length());
            return result;

        } catch (Exception e) {
            logger.error("使用{}算法加密失败", algorithmType.getDisplayName(), e);
            throw new RuntimeException("加密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据算法类型解密数据
     * 
     * @param ciphertext 密文
     * @param algorithmType 算法类型
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext, AlgorithmType algorithmType) {
        if (!StringUtils.hasText(ciphertext)) {
            return ciphertext;
        }

        try {
            logger.debug("使用{}算法解密数据，密文长度: {}", algorithmType.getDisplayName(), ciphertext.length());

            String result;
            switch (algorithmType) {
                case AES:
                    result = AESGCMUtil.decrypt(ciphertext);
                    break;
                case SM2:
                    result = SM2Util.decrypt(ciphertext);
                    break;
                case SM4:
                    // TODO: 实现SM4算法
                    throw new UnsupportedOperationException("SM4算法暂未实现");
                default:
                    throw new IllegalArgumentException("不支持的加密算法: " + algorithmType);
            }

            logger.debug("{}算法解密成功，明文长度: {}", algorithmType.getDisplayName(), result.length());
            return result;

        } catch (Exception e) {
            logger.error("使用{}算法解密失败", algorithmType.getDisplayName(), e);
            throw new RuntimeException("解密失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据字符串类型名称加密数据
     * 
     * @param plaintext 明文
     * @param algorithmName 算法名称
     * @return 加密后的密文
     */
    public static String encrypt(String plaintext, String algorithmName) {
        AlgorithmType algorithmType = parseAlgorithmType(algorithmName);
        return encrypt(plaintext, algorithmType);
    }

    /**
     * 根据字符串类型名称解密数据
     * 
     * @param ciphertext 密文
     * @param algorithmName 算法名称
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext, String algorithmName) {
        AlgorithmType algorithmType = parseAlgorithmType(algorithmName);
        return decrypt(ciphertext, algorithmType);
    }

    /**
     * 解析算法类型
     * 
     * @param algorithmName 算法名称
     * @return 算法类型
     */
    public static AlgorithmType parseAlgorithmType(String algorithmName) {
        if (!StringUtils.hasText(algorithmName)) {
            return AlgorithmType.AES; // 默认使用AES
        }

        String upperName = algorithmName.toUpperCase().trim();
        switch (upperName) {
            case "AES":
            case "AES-GCM":
                return AlgorithmType.AES;
            case "SM2":
                return AlgorithmType.SM2;
            case "SM4":
                return AlgorithmType.SM4;
            default:
                logger.warn("未知的算法类型: {}，使用默认AES算法", algorithmName);
                return AlgorithmType.AES;
        }
    }

    /**
     * 检查算法是否支持
     * 
     * @param algorithmType 算法类型
     * @return 是否支持
     */
    public static boolean isAlgorithmSupported(AlgorithmType algorithmType) {
        switch (algorithmType) {
            case AES:
            case SM2:
                return true;
            case SM4:
                return false; // 暂未实现
            default:
                return false;
        }
    }

    /**
     * 获取所有支持的算法信息
     * 
     * @return 算法信息数组
     */
    public static AlgorithmInfo[] getSupportedAlgorithms() {
        return new AlgorithmInfo[]{
            new AlgorithmInfo(AlgorithmType.AES, true, "默认推荐算法，性能优秀"),
            new AlgorithmInfo(AlgorithmType.SM2, true, "国密标准算法，符合国家安全要求"),
            new AlgorithmInfo(AlgorithmType.SM4, false, "国密对称算法，开发中")
        };
    }

    /**
     * 测试算法功能
     * 
     * @param algorithmType 算法类型
     * @return 测试结果
     */
    public static boolean testAlgorithm(AlgorithmType algorithmType) {
        try {
            String testData = "加密算法测试数据-" + System.currentTimeMillis();
            String encrypted = encrypt(testData, algorithmType);
            String decrypted = decrypt(encrypted, algorithmType);
            
            boolean success = testData.equals(decrypted);
            logger.info("{}算法测试{}", algorithmType.getDisplayName(), success ? "成功" : "失败");
            return success;
            
        } catch (Exception e) {
            logger.error("{}算法测试失败", algorithmType.getDisplayName(), e);
            return false;
        }
    }

    /**
     * 算法信息类
     */
    public static class AlgorithmInfo {
        private final AlgorithmType type;
        private final boolean supported;
        private final String note;

        public AlgorithmInfo(AlgorithmType type, boolean supported, String note) {
            this.type = type;
            this.supported = supported;
            this.note = note;
        }

        public AlgorithmType getType() {
            return type;
        }

        public boolean isSupported() {
            return supported;
        }

        public String getNote() {
            return note;
        }

        public String getName() {
            return type.name();
        }

        public String getDisplayName() {
            return type.getDisplayName();
        }

        public String getDescription() {
            return type.getDescription();
        }

        @Override
        public String toString() {
            return String.format("AlgorithmInfo{name='%s', displayName='%s', supported=%s, note='%s'}", 
                               getName(), getDisplayName(), supported, note);
        }
    }
}
