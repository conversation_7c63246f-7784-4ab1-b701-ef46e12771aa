<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="EasyCodeTableSetting">
    <option name="tableInfoMap">
      <map>
        <entry key="dyt_cultural.t_cultural_category">
          <value>
            <TableInfoDTO>
              <option name="comment" value="商品分类" />
              <option name="fullColumn">
                <list>
                  <ColumnInfoDTO>
                    <option name="comment" value="全局唯一标识" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="id" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="租户 ID" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="tenantId" />
                    <option name="type" value="java.lang.Long" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="商品分类名称" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="categoryName" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="分类图片URL" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="pictures" />
                    <option name="type" value="java.lang.String" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="行号（第几排）" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="lineNum" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="排序号" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="sort" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="comment" value="0:启用，1:禁用" />
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="status" />
                    <option name="type" value="java.lang.Integer" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="createTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                  <ColumnInfoDTO>
                    <option name="custom" value="false" />
                    <option name="ext" value="{}" />
                    <option name="name" value="updateTime" />
                    <option name="type" value="java.util.Date" />
                  </ColumnInfoDTO>
                </list>
              </option>
              <option name="name" value="TCulturalCategory" />
              <option name="preName" value="" />
              <option name="saveModelName" value="dyt-cultural" />
              <option name="savePackageName" value="com.ynhdkc.media.mall.dao" />
              <option name="savePath" value="./src/main/java/com/ynhdkc/media/mall/dao" />
              <option name="templateGroupName" value="MybatisPlus" />
            </TableInfoDTO>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" languageLevel="JDK_1_8" project-jdk-name="1.8" project-jdk-type="JavaSDK" />
</project>