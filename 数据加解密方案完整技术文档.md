# 数据加解密方案完整技术文档

## 文档概述

| 项目名称 | 肿瘤医院公众后台敏感数据加密方案 |
|---------|---------------------------|
| 文档版本 | v2.0.0 |
| 创建日期 | 2024-12-19 |
| 适用环境 | 阿里云RDS MySQL 5.7+ |
| 技术栈 | Spring Boot 2.7.x + JPA + MyBatis |

## 目录

1. [方案概述](#1-方案概述)
2. [需要加密的字段和表梳理](#2-需要加密的字段和表梳理)
3. [三种技术方案详解](#3-三种技术方案详解)
4. [应急回滚方案](#4-应急回滚方案)
5. [实施方案对比](#5-实施方案对比)
6. [推荐方案与实施计划](#6-推荐方案与实施计划)

## 1. 方案概述

### 1.1 背景需求

根据《数据安全法》和《个人信息保护法》要求，需要对肿瘤医院公众后台系统中的敏感数据进行加密存储，涉及约750万条记录，包含姓名、身份证号、手机号、银行卡号等敏感信息。

### 1.2 技术挑战

- **数据量大**：750万条记录，约2000万个敏感字段
- **共享RDS实例**：与其他数据库共用同一RDS实例，资源有限
- **并发限制**：需要控制并发度，避免影响其他业务
- **长事务限制**：必须避免长事务，防止锁表和资源占用
- **字段扩长**：加密后字段长度增加，需要扩展表结构
- **增量识别**：多表并行处理时需要精确识别增量数据
- **业务连续性要求高**：医疗系统不能长时间停机

### 1.3 解决方案

基于阿里云RDS MySQL环境，提供三种数据加密迁移技术方案：
1. **备份数据库增量迁移方案**：适用于可接受停服的场景
2. **新表替换方案**：适用于要求快速切换的场景
3. **渐进式影子字段方案**：适用于零停机要求的核心业务

**统一技术栈**：
- 所有方案都使用统一的加密注解系统（@EncryptField）
- 共用方案三的加密工具类和策略管理
- 支持JPA和MyBatis两种ORM框架
- 避免使用存储过程，全部使用Java应用层处理

## 2. 需要加密的字段和表梳理

### 2.1 核心敏感数据表（优先级：高）

| 表名 | 字段名 | 字段类型 | 数据量 | 加密类型 |
|------|--------|----------|---------|----------|
| **ych_patient** | name | varchar(60) | 776,493条 | 姓名 |
| | phone | varchar(11) | | 手机号 |
| | id_card_no | varchar(18) | | 身份证 |
| | address | varchar(255) | | 地址 |
| **ych_appoint_record** | patient_name | varchar(60) | 927,385条 | 姓名 |
| | patient_phone | varchar(20) | | 手机号 |
| | patient_id_card | varchar(50) | | 身份证 |

### 2.2 财务敏感数据表（优先级：高）

| 表名 | 字段名 | 字段类型 | 数据量 | 加密类型 |
|------|--------|----------|---------|----------|
| **ych_inpatient_settle_apply** | account_no | varchar(64) | 1,125条 | 银行卡号 |
| | account_name | varchar(32) | | 姓名 |
| | express_phone | varchar(32) | | 手机号 |
| | express_address | varchar(255) | | 地址 |

### 2.3 统计汇总

- **总计表数**：16个
- **总数据量**：约750万条记录
- **敏感字段实例数**：约2000万个
- **核心表**（数据量>10万）：5个

## 3. 统一加密组件设计

### 3.1 加密注解系统（三种方案共用）

```java
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface EncryptField {
    boolean enabled() default true;
    String description() default "";
    String shadowField() default "";
    MigrationStrategy migrationStrategy() default MigrationStrategy.DIRECT_ENCRYPT;
    String strategyKey() default "";
    int version() default 1;
}

public enum MigrationStrategy {
    DIRECT_ENCRYPT,      // 直接加密模式（方案一、二使用）
    PLAINTEXT_PRIORITY,  // 明文字段优先模式
    SHADOW_PRIORITY,     // 影子字段优先模式
    SHADOW_ONLY          // 仅影子字段模式
}
```

### 3.2 统一加密工具类

```java
@Component
public class EncryptionUtil {

    @Value("${encryption.secret-key}")
    private String secretKey;

    /**
     * AES-256-GCM加密
     */
    public String encrypt(String plaintext) {
        if (StringUtils.isEmpty(plaintext)) {
            return plaintext;
        }

        try {
            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);

            byte[] encryptedData = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            byte[] iv = cipher.getIV();

            // 将IV和加密数据组合
            byte[] encryptedWithIv = new byte[iv.length + encryptedData.length];
            System.arraycopy(iv, 0, encryptedWithIv, 0, iv.length);
            System.arraycopy(encryptedData, 0, encryptedWithIv, iv.length, encryptedData.length);

            return Base64.getEncoder().encodeToString(encryptedWithIv);
        } catch (Exception e) {
            throw new RuntimeException("Encryption failed", e);
        }
    }

    /**
     * AES-256-GCM解密
     */
    public String decrypt(String ciphertext) {
        if (StringUtils.isEmpty(ciphertext)) {
            return ciphertext;
        }

        try {
            byte[] encryptedWithIv = Base64.getDecoder().decode(ciphertext);

            // 提取IV和加密数据
            byte[] iv = new byte[12]; // GCM模式IV长度为12
            byte[] encryptedData = new byte[encryptedWithIv.length - 12];
            System.arraycopy(encryptedWithIv, 0, iv, 0, 12);
            System.arraycopy(encryptedWithIv, 12, encryptedData, 0, encryptedData.length);

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(), "AES");
            GCMParameterSpec gcmSpec = new GCMParameterSpec(128, iv);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmSpec);

            byte[] decryptedData = cipher.doFinal(encryptedData);
            return new String(decryptedData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("Decryption failed", e);
        }
    }
}
```

### 3.3 JPA加密转换器

```java
@Component
@Converter
public class EncryptConverter implements AttributeConverter<String, String> {

    @Autowired
    private EncryptionUtil encryptionUtil;

    @Override
    public String convertToDatabaseColumn(String attribute) {
        return encryptionUtil.encrypt(attribute);
    }

    @Override
    public String convertToEntityAttribute(String dbData) {
        return encryptionUtil.decrypt(dbData);
    }
}
```

### 3.4 MyBatis类型处理器

```java
@Component
@MappedTypes(String.class)
public class EncryptTypeHandler extends BaseTypeHandler<String> {

    @Autowired
    private EncryptionUtil encryptionUtil;

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, encryptionUtil.encrypt(parameter));
    }

    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String encrypted = rs.getString(columnName);
        return encryptionUtil.decrypt(encrypted);
    }

    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String encrypted = rs.getString(columnIndex);
        return encryptionUtil.decrypt(encrypted);
    }

    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String encrypted = cs.getString(columnIndex);
        return encryptionUtil.decrypt(encrypted);
    }
}
```

### 3.5 实体类配置示例

```java
@Entity
@Table(name = "ych_patient")
public class Patient {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 方案一、二：直接加密模式
    @EncryptField(
        description = "患者姓名",
        migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
        strategyKey = "name"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "name", length = 500) // 扩展长度适应加密数据
    private String name;

    @EncryptField(
        description = "手机号",
        migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
        strategyKey = "phone"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone", length = 500)
    private String phone;

    // 方案三：影子字段模式（可选配置）
    @EncryptField(
        description = "身份证号",
        shadowField = "id_card_no_encrypted",
        migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY,
        strategyKey = "idCardNo"
    )
    @Column(name = "id_card_no", length = 500)
    private String idCardNo;

    @Column(name = "id_card_no_encrypted", length = 500)
    private String idCardNoEncrypted; // 影子字段

    // 其他字段...
}
```

## 4. 三种技术方案详解

### 4.1 方案一：备份数据库增量迁移方案

#### 技术原理
通过数据库备份恢复机制，先处理存量数据，再通过停服处理增量数据，最后切换数据源。
**使用统一的加密注解和工具类，支持JPA和MyBatis两种ORM框架。**

#### 实施步骤

**1. 准备阶段**
```bash
# 创建备份数据库实例（阿里云RDS控制台）
# 全量备份生产数据库（仅备份相关表，减少资源占用）
mysqldump -h prod-rds-host -u username -p \
  --single-transaction \
  --routines \
  --triggers \
  --tables ych_patient ych_user ych_appoint_record ych_mc_apply \
  ych_inpatient_settle_apply ych_mi_his_pay_order ych_mi_wx_pay_order \
  > sensitive_tables_backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复到备份数据库
mysql -h backup-rds-host -u username -p < sensitive_tables_backup_*.sql

# 扩展字段长度以适应加密数据
mysql -h backup-rds-host -u username -p << EOF
ALTER TABLE ych_patient
MODIFY COLUMN name VARCHAR(500),
MODIFY COLUMN phone VARCHAR(500),
MODIFY COLUMN id_card_no VARCHAR(500),
MODIFY COLUMN address VARCHAR(500);

ALTER TABLE ych_user
MODIFY COLUMN name VARCHAR(500),
MODIFY COLUMN mobile VARCHAR(500),
MODIFY COLUMN idcard VARCHAR(500),
MODIFY COLUMN address VARCHAR(500);
-- 其他表的字段扩展...
EOF
```

**2. 存量数据加密处理（使用注解自动加密）**

**核心思路**：使用@EncryptField注解标注需要加密的字段，通过ORM框架的自动转换机制处理加密，无需手动调用加密算法。

**JPA方式实现：**
```java
@Service
public class BackupDataEncryptionService {

    @Autowired
    @Qualifier("backupPatientRepository")
    private PatientRepository backupPatientRepository; // 备份数据库Repository

    /**
     * 使用JPA自动加密机制处理存量数据
     * 只需要查询数据并重新保存，@Convert注解会自动处理加密
     */
    public void encryptSensitiveDataInBatches() {
        encryptPatientData();
        Thread.sleep(2000); // 表间间隔
        encryptUserData();
        Thread.sleep(2000);
        encryptAppointRecordData();
    }

    @Transactional("backupTransactionManager")
    public void encryptPatientData() {
        int batchSize = 100;
        int pageNumber = 0;

        log.info("Starting patient data encryption using JPA auto-conversion");

        while (true) {
            // 分页查询原始数据
            Pageable pageable = PageRequest.of(pageNumber, batchSize, Sort.by("id"));
            Page<Patient> patientPage = backupPatientRepository.findAll(pageable);

            if (patientPage.isEmpty()) {
                break;
            }

            // 重新保存数据，@Convert注解会自动触发加密
            List<Patient> patients = patientPage.getContent();
            backupPatientRepository.saveAll(patients);

            pageNumber++;

            // 控制处理速度
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }

            if (pageNumber % 10 == 0) {
                log.info("Patient data encryption progress: page {}", pageNumber);
            }
        }

        log.info("Patient data encryption completed");
    }
}
```

**MyBatis方式实现：**
```java
@Service
public class BackupDataEncryptionServiceMyBatis {

    @Autowired
    @Qualifier("backupSqlSessionTemplate")
    private SqlSessionTemplate backupSqlSession; // 备份数据库SqlSession

    /**
     * 使用MyBatis自动加密机制处理存量数据
     * 通过TypeHandler自动处理加密，无需手动调用加密算法
     */
    public void encryptPatientDataMyBatis() {
        int batchSize = 100;
        int offset = 0;

        log.info("Starting patient data encryption using MyBatis auto-conversion");

        while (true) {
            // 查询原始数据（不使用TypeHandler，获取明文）
            List<Patient> patients = backupSqlSession.selectList(
                "PatientMapper.selectPlaintext",
                Map.of("limit", batchSize, "offset", offset));

            if (patients.isEmpty()) {
                break;
            }

            // 重新保存数据（使用TypeHandler，自动加密）
            for (Patient patient : patients) {
                backupSqlSession.update("PatientMapper.updateWithEncryption", patient);
            }

            offset += batchSize;

            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }

            if (offset % 1000 == 0) {
                log.info("Patient data encryption progress: {} records", offset);
            }
        }

        log.info("Patient data encryption completed");
    }
}
```

**对应的MyBatis Mapper：**
```xml
<!-- PatientMapper.xml -->
<mapper namespace="PatientMapper">
    <!-- 查询明文数据（不使用TypeHandler） -->
    <select id="selectPlaintext" resultType="Patient">
        SELECT id, name, phone, id_card_no, address, created_at, updated_at
        FROM ych_patient
        WHERE name IS NOT NULL
        ORDER BY id
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 更新时自动加密（使用TypeHandler） -->
    <update id="updateWithEncryption" parameterType="Patient">
        UPDATE ych_patient SET
        name = #{name,typeHandler=EncryptTypeHandler},
        phone = #{phone,typeHandler=EncryptTypeHandler},
        id_card_no = #{idCardNo,typeHandler=EncryptTypeHandler},
        address = #{address,typeHandler=EncryptTypeHandler},
        updated_at = NOW()
        WHERE id = #{id}
    </update>
</mapper>
```

**实体类配置（方案一使用直接加密模式）：**
```java
@Entity
@Table(name = "ych_patient")
public class Patient {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    // 使用直接加密模式，JPA会自动处理加密/解密
    @EncryptField(
        description = "患者姓名",
        migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
        strategyKey = "name"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "name", length = 500) // 扩展长度适应加密数据
    private String name;

    @EncryptField(
        description = "手机号",
        migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
        strategyKey = "phone"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone", length = 500)
    private String phone;

    // 其他字段...
}
```

**3. 增量数据精确识别和处理**
```java
@Service
public class IncrementalDataProcessor {

    @Autowired
    private JdbcTemplate prodJdbcTemplate; // 生产数据库

    @Autowired
    private JdbcTemplate backupJdbcTemplate; // 备份数据库

    @Autowired
    private EncryptionUtil encryptionUtil;

    /**
     * 精确识别和处理增量数据
     * 基于时间戳和主键范围双重识别
     */
    public void processIncrementalData(LocalDateTime startTime) {
        // 为每个表单独处理增量数据
        processIncrementalPatientData(startTime);
        processIncrementalUserData(startTime);
        processIncrementalAppointRecordData(startTime);
        // ... 其他表
    }

    private void processIncrementalPatientData(LocalDateTime startTime) {
        // 1. 获取增量数据的ID范围（避免时间戳不准确的问题）
        String maxIdSql = "SELECT COALESCE(MAX(id), 0) FROM ych_patient WHERE updated_at < ?";
        Long maxProcessedId = backupJdbcTemplate.queryForObject(maxIdSql, Long.class, startTime);

        // 2. 查询增量数据（时间戳 + ID范围双重条件）
        String incrementalSql = "SELECT id, name, phone, id_card_no, address, updated_at " +
                               "FROM ych_patient " +
                               "WHERE (updated_at >= ? OR id > ?) " +
                               "AND name IS NOT NULL " +
                               "ORDER BY id";

        List<Map<String, Object>> incrementalData = prodJdbcTemplate.queryForList(
            incrementalSql, startTime, maxProcessedId);

        if (incrementalData.isEmpty()) {
            log.info("No incremental patient data found");
            return;
        }

        // 3. 分批处理增量数据，避免长事务
        int batchSize = 50; // 增量数据批次更小
        List<Object[]> batchArgs = new ArrayList<>();

        String upsertSql = "INSERT INTO ych_patient (id, name, phone, id_card_no, address, updated_at) " +
                          "VALUES (?, ?, ?, ?, ?, ?) " +
                          "ON DUPLICATE KEY UPDATE " +
                          "name=VALUES(name), phone=VALUES(phone), " +
                          "id_card_no=VALUES(id_card_no), address=VALUES(address), " +
                          "updated_at=VALUES(updated_at)";

        for (Map<String, Object> row : incrementalData) {
            Object[] args = new Object[6];
            args[0] = row.get("id");
            args[1] = encryptionUtil.encrypt((String) row.get("name"));
            args[2] = encryptionUtil.encrypt((String) row.get("phone"));
            args[3] = encryptionUtil.encrypt((String) row.get("id_card_no"));
            args[4] = encryptionUtil.encrypt((String) row.get("address"));
            args[5] = row.get("updated_at");
            batchArgs.add(args);

            if (batchArgs.size() >= batchSize) {
                backupJdbcTemplate.batchUpdate(upsertSql, batchArgs);
                batchArgs.clear();

                // 控制处理速度
                try {
                    Thread.sleep(50);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        // 处理剩余数据
        if (!batchArgs.isEmpty()) {
            backupJdbcTemplate.batchUpdate(upsertSql, batchArgs);
        }

        log.info("Processed {} incremental patient records", incrementalData.size());
    }
}
```

**4. 数据源切换**
```bash
# 修改应用配置指向备份数据库
sed -i 's/prod-rds-host/backup-rds-host/g' application.yml

# 启动应用服务
systemctl start application-service

# 验证服务正常
curl -f http://localhost:8080/health || exit 1
```

#### 方案特点
- **优势**：存量数据处理时间充足，数据一致性有保障
- **劣势**：需要额外数据库实例成本，切换过程需要停服
- **停服时间**：2-4小时
- **适用场景**：数据量大，可接受停服的场景

### 4.2 方案二：新表替换方案

#### 技术原理
创建新的表结构，将加密后的数据插入新表，通过表重命名实现快速切换。
**使用统一的加密注解和工具类，支持JPA和MyBatis两种ORM框架。**

#### 实施步骤

**1. 创建新表结构（分批执行，避免锁表）**
```sql
-- 分别为每个表创建新表，避免同时锁定多个表
-- 第一批：核心表
CREATE TABLE ych_patient_new LIKE ych_patient;
ALTER TABLE ych_patient_new
MODIFY COLUMN name VARCHAR(500),
MODIFY COLUMN phone VARCHAR(500),
MODIFY COLUMN id_card_no VARCHAR(500),
MODIFY COLUMN address VARCHAR(500);

-- 等待一段时间，避免对共享RDS造成压力
-- 第二批：用户表
CREATE TABLE ych_user_new LIKE ych_user;
ALTER TABLE ych_user_new
MODIFY COLUMN name VARCHAR(500),
MODIFY COLUMN mobile VARCHAR(500),
MODIFY COLUMN idcard VARCHAR(500),
MODIFY COLUMN address VARCHAR(500);

-- 第三批：预约记录表
CREATE TABLE ych_appoint_record_new LIKE ych_appoint_record;
ALTER TABLE ych_appoint_record_new
MODIFY COLUMN patient_name VARCHAR(500),
MODIFY COLUMN patient_phone VARCHAR(500),
MODIFY COLUMN patient_id_card VARCHAR(500);

-- 其他表依次创建...
```

**2. 数据加密迁移（使用注解自动加密）**

**核心思路**：原表和新表都使用相同的@EncryptField注解，通过ORM框架自动处理数据转换，无需手动调用加密算法。

**JPA方式实现：**
```java
@Service
public class NewTableMigrationService {

    @Autowired
    private PatientRepository patientRepository; // 原表Repository

    @Autowired
    private PatientNewRepository patientNewRepository; // 新表Repository

    /**
     * 使用JPA自动加密机制进行数据迁移
     * 只需要从原表查询数据并保存到新表，@Convert注解会自动处理加密
     */
    public void migrateAllTables() {
        migratePatientData();
        Thread.sleep(5000); // 表间间隔
        migrateUserData();
        Thread.sleep(5000);
        migrateAppointRecordData();
    }

    @Transactional
    public void migratePatientData() {
        int batchSize = 100;
        int pageNumber = 0;

        log.info("Starting patient data migration using JPA auto-conversion");

        while (true) {
            // 分页查询原表数据
            Pageable pageable = PageRequest.of(pageNumber, batchSize, Sort.by("id"));
            Page<Patient> patientPage = patientRepository.findAll(pageable);

            if (patientPage.isEmpty()) {
                break;
            }

            // 转换并保存到新表（自动加密）
            List<PatientNew> newPatients = new ArrayList<>();
            for (Patient patient : patientPage.getContent()) {
                PatientNew newPatient = new PatientNew();
                newPatient.setId(patient.getId());
                // 直接赋值，@Convert注解会自动处理加密
                newPatient.setName(patient.getName());
                newPatient.setPhone(patient.getPhone());
                newPatient.setIdCardNo(patient.getIdCardNo());
                newPatient.setAddress(patient.getAddress());
                newPatient.setCreatedAt(patient.getCreatedAt());
                newPatient.setUpdatedAt(patient.getUpdatedAt());
                newPatients.add(newPatient);
            }

            // 批量保存到新表
            patientNewRepository.saveAll(newPatients);

            pageNumber++;

            // 控制处理速度
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }

            if (pageNumber % 10 == 0) {
                log.info("Patient data migration progress: page {}", pageNumber);
            }
        }

        log.info("Patient data migration completed");
    }

    /**
     * 处理增量数据
     */
    @Transactional
    public void handleIncrementalData(LocalDateTime migrationStartTime) {
        List<Patient> incrementalPatients = patientRepository
            .findByUpdatedAtGreaterThanEqualOrderById(migrationStartTime);

        if (incrementalPatients.isEmpty()) {
            log.info("No incremental patient data found");
            return;
        }

        for (Patient patient : incrementalPatients) {
            // 查找或创建新表记录
            PatientNew newPatient = patientNewRepository.findById(patient.getId())
                .orElse(new PatientNew());

            newPatient.setId(patient.getId());
            // 直接赋值，自动加密
            newPatient.setName(patient.getName());
            newPatient.setPhone(patient.getPhone());
            newPatient.setIdCardNo(patient.getIdCardNo());
            newPatient.setAddress(patient.getAddress());
            newPatient.setUpdatedAt(patient.getUpdatedAt());

            patientNewRepository.save(newPatient);
        }

        log.info("Processed {} incremental patient records", incrementalPatients.size());
    }
}
```

**MyBatis方式实现：**
```java
@Service
public class NewTableMigrationServiceMyBatis {

    @Autowired
    private SqlSessionTemplate sqlSession;

    /**
     * 使用MyBatis自动加密机制进行数据迁移
     * 通过TypeHandler自动处理加密，无需手动调用加密算法
     */
    public void migratePatientDataMyBatis() {
        int batchSize = 100;
        int offset = 0;

        log.info("Starting patient data migration using MyBatis auto-conversion");

        while (true) {
            // 查询原表数据（明文）
            List<Patient> patients = sqlSession.selectList(
                "PatientMapper.selectForMigration",
                Map.of("limit", batchSize, "offset", offset));

            if (patients.isEmpty()) {
                break;
            }

            // 批量插入新表（TypeHandler自动加密）
            sqlSession.insert("PatientNewMapper.batchInsertWithEncryption", patients);

            offset += batchSize;

            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }

            if (offset % 1000 == 0) {
                log.info("Patient data migration progress: {} records", offset);
            }
        }

        log.info("Patient data migration completed");
    }

    /**
     * 处理增量数据
     */
    public void handleIncrementalDataMyBatis(LocalDateTime migrationStartTime) {
        List<Patient> incrementalPatients = sqlSession.selectList(
            "PatientMapper.selectIncremental", migrationStartTime);

        if (!incrementalPatients.isEmpty()) {
            // 使用UPSERT语句处理增量数据（TypeHandler自动加密）
            sqlSession.update("PatientNewMapper.upsertBatchWithEncryption", incrementalPatients);
            log.info("Processed {} incremental patient records", incrementalPatients.size());
        }
    }
}
```

**对应的MyBatis Mapper：**
```xml
<!-- PatientMapper.xml -->
<mapper namespace="PatientMapper">
    <!-- 查询原表数据（明文，不使用TypeHandler） -->
    <select id="selectForMigration" resultType="Patient">
        SELECT id, name, phone, id_card_no, address, created_at, updated_at
        FROM ych_patient
        ORDER BY id
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <select id="selectIncremental" resultType="Patient">
        SELECT id, name, phone, id_card_no, address, created_at, updated_at
        FROM ych_patient
        WHERE updated_at >= #{migrationStartTime}
        ORDER BY id
    </select>
</mapper>

<!-- PatientNewMapper.xml -->
<mapper namespace="PatientNewMapper">
    <!-- 批量插入新表（使用TypeHandler自动加密） -->
    <insert id="batchInsertWithEncryption" parameterType="list">
        INSERT INTO ych_patient_new (id, name, phone, id_card_no, address, created_at, updated_at)
        VALUES
        <foreach collection="list" item="patient" separator=",">
            (#{patient.id},
             #{patient.name,typeHandler=EncryptTypeHandler},
             #{patient.phone,typeHandler=EncryptTypeHandler},
             #{patient.idCardNo,typeHandler=EncryptTypeHandler},
             #{patient.address,typeHandler=EncryptTypeHandler},
             #{patient.createdAt},
             #{patient.updatedAt})
        </foreach>
    </insert>

    <!-- UPSERT增量数据（使用TypeHandler自动加密） -->
    <insert id="upsertBatchWithEncryption" parameterType="list">
        INSERT INTO ych_patient_new (id, name, phone, id_card_no, address, created_at, updated_at)
        VALUES
        <foreach collection="list" item="patient" separator=",">
            (#{patient.id},
             #{patient.name,typeHandler=EncryptTypeHandler},
             #{patient.phone,typeHandler=EncryptTypeHandler},
             #{patient.idCardNo,typeHandler=EncryptTypeHandler},
             #{patient.address,typeHandler=EncryptTypeHandler},
             #{patient.createdAt},
             #{patient.updatedAt})
        </foreach>
        ON DUPLICATE KEY UPDATE
        name = VALUES(name),
        phone = VALUES(phone),
        id_card_no = VALUES(id_card_no),
        address = VALUES(address),
        updated_at = VALUES(updated_at)
    </insert>
</mapper>
```

**新表实体类配置（使用直接加密模式）：**
```java
@Entity
@Table(name = "ych_patient_new")
public class PatientNew {
    @Id
    private Long id;

    // 新表使用直接加密模式，保存时自动加密
    @EncryptField(
        description = "患者姓名",
        migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
        strategyKey = "name"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "name", length = 500)
    private String name;

    @EncryptField(
        description = "手机号",
        migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
        strategyKey = "phone"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone", length = 500)
    private String phone;

    // 其他字段...
}
```

**3. 数据一致性验证**
```sql
-- 验证数据条数一致性
SELECT
    (SELECT COUNT(*) FROM ych_patient) as original_count,
    (SELECT COUNT(*) FROM ych_patient_new) as new_count;

-- 验证关键字段非空
SELECT COUNT(*) FROM ych_patient_new
WHERE name IS NULL OR phone IS NULL OR id_card_no IS NULL;
```

**4. 停服切换表名**
```bash
# 停止应用服务
systemctl stop application-service

# 执行表重命名（原子操作）
mysql -h rds-host -u username -p << EOF
START TRANSACTION;

-- 重命名原表为备份表
RENAME TABLE
    ych_patient TO ych_patient_backup,
    ych_user TO ych_user_backup,
    ych_appoint_record TO ych_appoint_record_backup;

-- 重命名新表为正式表
RENAME TABLE
    ych_patient_new TO ych_patient,
    ych_user_new TO ych_user,
    ych_appoint_record_new TO ych_appoint_record;

COMMIT;
EOF

# 启动应用服务
systemctl start application-service
```

#### 方案特点
- **优势**：切换过程极快（秒级），原表作为备份保留，回滚简单快速
- **劣势**：需要双倍存储空间，迁移过程中数据可能不一致
- **停服时间**：5-15分钟
- **适用场景**：数据量中等，要求快速切换的场景

### 4.3 方案三：渐进式影子字段方案（统一加密组件的完整实现）

#### 技术原理
在现有表中添加影子字段，通过多种迁移策略实现渐进式迁移，支持配置驱动的策略管理。
**这是统一加密组件的完整实现，包含了所有加密注解、工具类、策略管理等核心功能。**

#### 核心特性
- 零停机迁移
- 多种迁移策略（明文优先、影子字段优先、仅影子字段）
- 配置驱动的策略管理
- 秒级快速回滚

#### 实施步骤

**1. 添加影子字段**
```sql
-- 为敏感字段添加加密字段
ALTER TABLE ych_patient
ADD COLUMN name_encrypted VARCHAR(500) COMMENT '姓名加密字段',
ADD COLUMN phone_encrypted VARCHAR(500) COMMENT '手机号加密字段',
ADD COLUMN id_card_no_encrypted VARCHAR(500) COMMENT '身份证号加密字段',
ADD COLUMN address_encrypted VARCHAR(500) COMMENT '地址加密字段',
ADD COLUMN data_version INT DEFAULT 1 COMMENT '数据版本',
ADD COLUMN migration_status VARCHAR(20) DEFAULT 'NOT_MIGRATED' COMMENT '迁移状态';
```

**2. 配置迁移策略**
```yaml
# application.yml
encryption:
  migration:
    default-strategy: PLAINTEXT_PRIORITY
    enable-global-override: false
    strategies:
      name: PLAINTEXT_PRIORITY      # 姓名：明文优先
      phone: PLAINTEXT_PRIORITY     # 手机：明文优先
      idCardNo: PLAINTEXT_PRIORITY  # 身份证：明文优先
      address: PLAINTEXT_PRIORITY   # 地址：明文优先
```

**3. 实体类配置**
```java
@Entity
@Table(name = "ych_patient")
public class Patient {
    @EncryptField(
        shadowField = "name_encrypted",
        migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY,
        strategyKey = "name"
    )
    private String name;

    @EncryptField(
        shadowField = "phone_encrypted",
        migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY,
        strategyKey = "phone"
    )
    private String phone;

    // 影子字段
    @Column(name = "name_encrypted")
    private String nameEncrypted;

    @Column(name = "phone_encrypted")
    private String phoneEncrypted;
}
```

**4. 渐进式迁移执行**
```bash
# 阶段1：部署应用（明文优先策略）
# 新数据自动双写，读取仍使用明文

# 阶段2：批量迁移存量数据
curl -X POST http://localhost:8080/api/migration/migrate/2

# 阶段3：切换到影子字段优先
curl -X POST http://localhost:8080/api/strategy/switch/shadow

# 阶段4：最终切换到仅影子字段
curl -X POST http://localhost:8080/api/strategy/global \
  -H "Content-Type: application/json" \
  -d '{"strategy": "SHADOW_ONLY", "enableOverride": true}'

# 阶段5：启动明文字段清理任务（基于xxl-job）
curl -X POST http://localhost:8080/api/migration/cleanup/start

# 监控清理进度
curl http://localhost:8080/api/migration/cleanup/status
```

**5. 明文字段清理方案（基于xxl-job定时任务）**

在完成策略切换到仅影子字段后，需要清理明文字段以彻底消除数据泄露风险。

**清理策略**：
- 使用xxl-job定时任务调度，确保清理过程可控
- 按表逐一清理，避免对数据库造成过大压力
- 每次只清理一部分数据，分批次执行
- 支持暂停、恢复、回滚操作

**技术实现**：
```java
@Component
@Slf4j
public class PlaintextCleanupJobHandler {

    @Autowired
    private PlaintextCleanupService cleanupService;

    /**
     * xxl-job定时任务：清理明文字段
     * 执行参数格式：tableName=ych_patient,batchSize=100,delayMs=200
     */
    @XxlJob("plaintextCleanupJob")
    public void plaintextCleanupJob() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行明文字段清理任务，参数：{}", param);

        try {
            // 解析任务参数
            Map<String, String> params = parseJobParams(param);
            String tableName = params.get("tableName");
            int batchSize = Integer.parseInt(params.getOrDefault("batchSize", "100"));
            int delayMs = Integer.parseInt(params.getOrDefault("delayMs", "200"));

            // 执行清理任务
            CleanupResult result = cleanupService.cleanupTable(tableName, batchSize, delayMs);

            if (result.isCompleted()) {
                XxlJobHelper.handleSuccess("表 " + tableName + " 清理完成，共处理 " + result.getProcessedCount() + " 条记录");
            } else {
                XxlJobHelper.handleSuccess("表 " + tableName + " 本批次处理完成，已处理 " + result.getProcessedCount() + " 条记录");
            }

        } catch (Exception e) {
            log.error("明文字段清理任务执行失败", e);
            XxlJobHelper.handleFail("清理任务执行失败：" + e.getMessage());
        }
    }
}

@Service
@Slf4j
public class PlaintextCleanupService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String CLEANUP_STATUS_KEY = "cleanup:status:";
    private static final String CLEANUP_PROGRESS_KEY = "cleanup:progress:";

    /**
     * 清理指定表的明文字段
     */
    @Transactional
    public CleanupResult cleanupTable(String tableName, int batchSize, int delayMs) {
        // 检查清理状态
        String statusKey = CLEANUP_STATUS_KEY + tableName;
        String status = (String) redisTemplate.opsForValue().get(statusKey);

        if ("PAUSED".equals(status)) {
            log.info("表 {} 的清理任务已暂停", tableName);
            return CleanupResult.paused();
        }

        // 获取清理进度
        String progressKey = CLEANUP_PROGRESS_KEY + tableName;
        Long lastProcessedId = (Long) redisTemplate.opsForValue().get(progressKey);
        if (lastProcessedId == null) {
            lastProcessedId = 0L;
        }

        // 根据表名选择清理策略
        switch (tableName) {
            case "ych_patient":
                return cleanupPatientTable(lastProcessedId, batchSize, delayMs);
            case "ych_user":
                return cleanupUserTable(lastProcessedId, batchSize, delayMs);
            case "ych_appoint_record":
                return cleanupAppointRecordTable(lastProcessedId, batchSize, delayMs);
            default:
                throw new IllegalArgumentException("不支持的表名：" + tableName);
        }
    }

    /**
     * 清理患者表明文字段
     */
    private CleanupResult cleanupPatientTable(Long lastProcessedId, int batchSize, int delayMs) {
        String tableName = "ych_patient";

        // 查询需要清理的记录
        String selectSql = "SELECT id FROM ych_patient " +
                          "WHERE id > ? AND migration_status = 'COMPLETED' " +
                          "AND (name IS NOT NULL OR phone IS NOT NULL OR id_card_no IS NOT NULL) " +
                          "ORDER BY id LIMIT ?";

        List<Long> ids = jdbcTemplate.queryForList(selectSql, Long.class, lastProcessedId, batchSize);

        if (ids.isEmpty()) {
            // 清理完成
            redisTemplate.delete(CLEANUP_PROGRESS_KEY + tableName);
            redisTemplate.delete(CLEANUP_STATUS_KEY + tableName);
            log.info("表 {} 明文字段清理完成", tableName);
            return CleanupResult.completed(0);
        }

        // 批量清理明文字段
        String updateSql = "UPDATE ych_patient SET " +
                          "name = NULL, " +
                          "phone = NULL, " +
                          "id_card_no = NULL, " +
                          "address = NULL, " +
                          "cleanup_time = NOW() " +
                          "WHERE id IN (" + String.join(",", Collections.nCopies(ids.size(), "?")) + ")";

        Object[] params = ids.toArray();
        int updatedCount = jdbcTemplate.update(updateSql, params);

        // 更新进度
        Long maxId = ids.get(ids.size() - 1);
        redisTemplate.opsForValue().set(CLEANUP_PROGRESS_KEY + tableName, maxId);

        // 控制清理速度
        if (delayMs > 0) {
            try {
                Thread.sleep(delayMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        log.info("表 {} 本批次清理完成，处理记录数：{}，最大ID：{}", tableName, updatedCount, maxId);
        return CleanupResult.processing(updatedCount);
    }
}
```

**xxl-job任务配置**：
```yaml
# xxl-job配置
xxl:
  job:
    admin:
      addresses: http://xxl-job-admin:8080/xxl-job-admin
    executor:
      appname: dyt-data-encryption
      address:
      ip:
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
```

**清理任务调度配置**：
```bash
# 在xxl-job管理后台配置以下任务：

# 任务1：清理患者表
任务名称：患者表明文字段清理
Cron表达式：0 */10 * * * ?  # 每10分钟执行一次
执行器：dyt-data-encryption
JobHandler：plaintextCleanupJob
执行参数：tableName=ych_patient,batchSize=100,delayMs=200

# 任务2：清理用户表
任务名称：用户表明文字段清理
Cron表达式：0 5 */1 * * ?   # 每小时的第5分钟执行
执行器：dyt-data-encryption
JobHandler：plaintextCleanupJob
执行参数：tableName=ych_user,batchSize=50,delayMs=300

# 任务3：清理预约记录表
任务名称：预约记录表明文字段清理
Cron表达式：0 15 */2 * * ?  # 每2小时的第15分钟执行
执行器：dyt-data-encryption
JobHandler：plaintextCleanupJob
执行参数：tableName=ych_appoint_record,batchSize=200,delayMs=100
```

**清理安全措施**：
- **数据备份**：清理前自动备份明文数据到专用备份表
- **分批处理**：每批次处理100-200条记录，避免长事务
- **进度控制**：支持暂停、恢复、查看进度
- **回滚机制**：保留备份数据，支持紧急回滚
- **监控告警**：清理异常时自动告警

#### 方案特点
- **优势**：零停机迁移，秒级快速回滚，渐进式验证，配置驱动管理
- **劣势**：需要修改应用代码，增加存储空间，迁移周期较长
- **停服时间**：0分钟
- **适用场景**：核心业务，不能停服的场景

## 4. 应急回滚方案

### 4.1 方案一应急回滚

#### 回滚触发条件
- 数据加密后发现业务功能异常
- 系统性能严重下降
- 数据一致性问题

#### 回滚步骤
```bash
# 1. 立即停止应用服务
systemctl stop application-service

# 2. 切换回原数据库
sed -i 's/backup-rds-host/prod-rds-host/g' application.yml

# 3. 恢复原数据库（如果已被修改）
mysql -h prod-rds-host -u username -p < original_backup_$(date +%Y%m%d).sql

# 4. 启动应用服务
systemctl start application-service

# 5. 验证服务正常
curl -f http://localhost:8080/health
```

**回滚时间**：2-4小时
**数据丢失风险**：中等（增量数据可能丢失）

### 4.2 方案二应急回滚

#### 回滚触发条件
- 新表数据异常
- 应用无法正常访问新表
- 性能问题

#### 回滚步骤
```bash
# 1. 停止应用服务
systemctl stop application-service

# 2. 执行表回滚（原子操作）
mysql -h rds-host -u username -p << EOF
START TRANSACTION;

-- 删除新表
DROP TABLE ych_patient, ych_user, ych_appoint_record;

-- 恢复原表
RENAME TABLE
    ych_patient_backup TO ych_patient,
    ych_user_backup TO ych_user,
    ych_appoint_record_backup TO ych_appoint_record;

COMMIT;
EOF

# 3. 启动应用服务
systemctl start application-service
```

**回滚时间**：5-15分钟
**数据丢失风险**：低（原表完整保留）

### 4.3 方案三应急回滚

#### 回滚触发条件
- 加密数据读取异常
- 性能问题
- 业务功能异常

#### 快速回滚（秒级）
```bash
# 1. 紧急回滚到明文优先模式
curl -X POST http://localhost:8080/api/strategy/rollback/plaintext

# 2. 验证回滚效果
curl http://localhost:8080/api/strategy/config
```

#### 完整回滚
```bash
# 1. 停止应用服务（可选）
systemctl stop application-service

# 2. 修改配置文件
cat > application.yml << EOF
encryption:
  migration:
    default-strategy: PLAINTEXT_PRIORITY
    enable-global-override: true
    strategies: {}
EOF

# 3. 启动应用服务
systemctl start application-service

# 4. 清理影子字段数据（可选）
mysql -h rds-host -u username -p << EOF
UPDATE ych_patient SET
    name_encrypted = NULL,
    phone_encrypted = NULL,
    id_card_no_encrypted = NULL,
    data_version = 1,
    migration_status = 'ROLLBACK';
EOF
```

**回滚时间**：秒级（快速回滚）/ 30分钟（完整回滚）
**数据丢失风险**：极低（明文数据完整保留）

## 5. 实施方案对比

### 5.1 综合对比表

| 对比维度 | 方案一：备份数据库增量迁移 | 方案二：新表替换 | 方案三：渐进式影子字段 |
|---------|----------------------|----------------|-------------------|
| **停服时间** | 2-4小时 | 5-15分钟 | 0分钟 |
| **技术复杂度** | 中等 | 简单 | 高 |
| **存储成本** | 高（需要备份数据库） | 高（双倍存储空间） | 中等（增加影子字段） |
| **回滚难度** | 高 | 低 | 极低 |
| **回滚时间** | 2-4小时 | 5-15分钟 | 秒级 |
| **数据一致性风险** | 低 | 中 | 极低 |
| **业务影响** | 高（长时间停服） | 中（短时间停服） | 极低（零停机） |
| **代码修改量** | 少 | 少 | 多 |
| **运维复杂度** | 高 | 中 | 低 |
| **验证周期** | 短 | 短 | 长 |
| **适用场景** | 数据量大，可接受停服 | 数据量中等，要求快速切换 | 核心业务，不能停服 |

### 5.2 资源消耗分析

#### 方案一资源消耗
- **RDS资源**：需要额外的RDS实例，对共享资源影响最大
- **并发控制**：备份数据库独立处理，不影响生产环境并发
- **事务控制**：Java应用分批处理，避免长事务
- **网络带宽**：数据库间数据传输，消耗网络资源

#### 方案二资源消耗
- **存储空间**：需要双倍存储空间，临时占用较大
- **并发控制**：小批次处理（100条/批），低并发影响
- **事务控制**：每批独立事务，避免长事务锁表
- **CPU/内存**：加密计算消耗CPU，批量处理消耗内存

#### 方案三资源消耗
- **存储空间**：增加影子字段，存储空间增加约30%
- **并发控制**：渐进式处理，对并发影响最小
- **事务控制**：JPA自动管理，单条记录事务
- **应用复杂度**：代码复杂度高，但运行时资源消耗稳定

### 5.3 风险评估

#### 技术风险
- **方案一**：数据库切换风险高，增量数据处理复杂
- **方案二**：表重命名过程中的锁表风险
- **方案三**：代码复杂度高，需要充分测试

#### 业务风险
- **方案一**：长时间停服影响医院正常业务
- **方案二**：短时间停服，影响相对较小
- **方案三**：零停机，业务风险最低

#### 数据风险
- **方案一**：增量数据可能丢失
- **方案二**：迁移过程中数据不一致
- **方案三**：数据双重保障，风险最低

## 6. 推荐方案与实施计划

### 6.1 推荐方案

**综合评估结果**：推荐采用**方案三：渐进式影子字段方案**

#### 推荐理由
1. **业务连续性**：零停机迁移，不影响医院正常业务
2. **风险可控**：渐进式迁移，每个阶段都可验证和回滚
3. **快速响应**：秒级回滚能力，应急响应能力强
4. **技术先进**：配置驱动的策略管理，运维友好
5. **符合医疗行业特点**：医疗业务对连续性要求极高
6. **共享RDS友好**：对共享RDS实例影响最小，资源消耗稳定
7. **并发控制优秀**：渐进式处理，不会产生高并发冲击

#### 备选方案
- **主方案**：方案三（渐进式影子字段）
- **备选方案**：方案二（新表替换）
- **应急方案**：方案一（备份数据库增量迁移）

### 6.2 实施计划

#### 阶段一：环境准备（1-2天）
1. **测试环境搭建**
   - 复制生产数据库到测试环境
   - 数据脱敏处理
   - 监控系统配置

2. **代码部署准备**
   - 创建功能分支
   - 部署到测试环境

#### 阶段二：功能开发（5-7天）
1. **加密组件开发**
   - AES-256-GCM加密工具类
   - JPA AttributeConverter
   - 加密注解和策略枚举

2. **影子字段管理器开发**
   - 字段元数据缓存
   - 读写策略处理逻辑
   - JPA生命周期回调

3. **配置管理系统开发**
   - 策略配置类
   - 策略管理控制器
   - 运行时策略更新

4. **迁移服务开发**
   - 批量迁移功能
   - 版本管理机制
   - 统计分析功能

#### 阶段三：测试验证（3-5天）
1. **功能测试**
   - 基础功能测试
   - 策略切换测试
   - 数据一致性验证

2. **性能测试**
   - 压力测试（JMeter）
   - 响应时间监控
   - 吞吐量测试

3. **安全测试**
   - 加密强度验证
   - 数据泄露测试
   - 权限绕过测试

#### 阶段四：生产部署（1-2天）
1. **数据库变更**
   - 添加影子字段
   - 创建索引

2. **应用部署**
   - 备份当前版本
   - 部署新版本
   - 验证部署成功

3. **数据迁移执行**
   - 执行批量迁移
   - 监控迁移进度
   - 验证迁移结果

#### 阶段五：策略切换（分阶段执行）
1. **第一阶段：明文优先（1周观察期）**
2. **第二阶段：影子字段优先（1周观察期）**
3. **第三阶段：仅影子字段（最终状态）**

#### 阶段六：明文字段清理（1-2周）
1. **xxl-job任务配置**
   - 配置清理任务调度
   - 设置合理的执行频率
   - 配置监控告警

2. **分表清理执行**
   - 按表优先级逐一清理
   - 核心表优先（患者表、用户表）
   - 监控清理进度和性能影响

3. **清理验证**
   - 验证明文字段已清空
   - 确认影子字段数据完整
   - 业务功能正常验证

### 6.3 成功标准

#### 技术指标
- 数据加密率：100%
- 明文字段清理率：100%
- 系统性能下降：< 5%
- 数据一致性：100%
- 回滚时间：< 30秒
- 清理任务成功率：> 99%

#### 业务指标
- 业务中断时间：0分钟
- 用户投诉：0起
- 数据安全事件：0起

#### 合规指标
- 符合《数据安全法》要求
- 通过安全审计
- 满足等保要求

### 6.4 风险控制措施

#### 技术风险控制
1. **数据备份策略**
   - 每个阶段执行前进行全量备份
   - 实时增量备份机制
   - 多地域备份存储

2. **监控告警机制**
   - 关键指标监控
   - 异常自动告警
   - 自动回滚机制

3. **灰度发布策略**
   - 按用户群体分批发布
   - 按功能模块分批发布
   - 实时监控用户反馈

#### 业务风险控制
1. **业务连续性保障**
   - 核心业务功能优先保障
   - 非核心功能可降级处理
   - 应急处理预案

2. **用户体验保障**
   - 加密过程对用户透明
   - 响应时间不明显增加
   - 操作流程不变

3. **合规风险控制**
   - 严格按照数据安全法要求
   - 定期安全评估
   - 审计日志完整性保障

## 总结

本技术文档详细阐述了三种数据加密迁移方案，经过综合评估，推荐采用渐进式影子字段方案。该方案虽然技术复杂度较高，但能够实现零停机迁移，具有秒级回滚能力，最适合医疗行业的业务特点和安全要求。

通过分阶段实施和严格的风险控制措施，可以确保数据加密迁移的安全性、可靠性和业务连续性，满足法规合规要求，为医院信息系统的数据安全提供强有力的保障。
