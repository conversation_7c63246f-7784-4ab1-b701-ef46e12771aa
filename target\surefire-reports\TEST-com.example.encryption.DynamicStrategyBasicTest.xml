<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.example.encryption.DynamicStrategyBasicTest" time="0.724" tests="8" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption\target\test-classes;D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption\target\classes;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-web\2.3.12.RELEASE\spring-boot-starter-web-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter\2.3.12.RELEASE\spring-boot-starter-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot\2.3.12.RELEASE\spring-boot-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-autoconfigure\2.3.12.RELEASE\spring-boot-autoconfigure-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-logging\2.3.12.RELEASE\spring-boot-starter-logging-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\env\dyt_mavenRepository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\env\dyt_mavenRepository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;D:\env\dyt_mavenRepository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;D:\env\dyt_mavenRepository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;D:\env\dyt_mavenRepository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\env\dyt_mavenRepository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-json\2.3.12.RELEASE\spring-boot-starter-json-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-databind\2.11.4\jackson-databind-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-annotations\2.11.4\jackson-annotations-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-core\2.11.4\jackson-core-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-tomcat\2.3.12.RELEASE\spring-boot-starter-tomcat-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\apache\tomcat\embed\tomcat-embed-core\9.0.46\tomcat-embed-core-9.0.46.jar;D:\env\dyt_mavenRepository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.46\tomcat-embed-websocket-9.0.46.jar;D:\env\dyt_mavenRepository\org\springframework\spring-web\5.2.15.RELEASE\spring-web-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-beans\5.2.15.RELEASE\spring-beans-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-webmvc\5.2.15.RELEASE\spring-webmvc-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-aop\5.2.15.RELEASE\spring-aop-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-context\5.2.15.RELEASE\spring-context-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-expression\5.2.15.RELEASE\spring-expression-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-data-jpa\2.3.12.RELEASE\spring-boot-starter-data-jpa-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-aop\2.3.12.RELEASE\spring-boot-starter-aop-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-jdbc\2.3.12.RELEASE\spring-boot-starter-jdbc-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;D:\env\dyt_mavenRepository\org\springframework\spring-jdbc\5.2.15.RELEASE\spring-jdbc-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;D:\env\dyt_mavenRepository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;D:\env\dyt_mavenRepository\org\hibernate\hibernate-core\5.4.32.Final\hibernate-core-5.4.32.Final.jar;D:\env\dyt_mavenRepository\org\jboss\logging\jboss-logging\3.4.2.Final\jboss-logging-3.4.2.Final.jar;D:\env\dyt_mavenRepository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;D:\env\dyt_mavenRepository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;D:\env\dyt_mavenRepository\antlr\antlr\2.7.7\antlr-2.7.7.jar;D:\env\dyt_mavenRepository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;D:\env\dyt_mavenRepository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\env\dyt_mavenRepository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;D:\env\dyt_mavenRepository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;D:\env\dyt_mavenRepository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;D:\env\dyt_mavenRepository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;D:\env\dyt_mavenRepository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;D:\env\dyt_mavenRepository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\env\dyt_mavenRepository\org\springframework\data\spring-data-jpa\2.3.9.RELEASE\spring-data-jpa-2.3.9.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\data\spring-data-commons\2.3.9.RELEASE\spring-data-commons-2.3.9.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-orm\5.2.15.RELEASE\spring-orm-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-tx\5.2.15.RELEASE\spring-tx-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;D:\env\dyt_mavenRepository\org\springframework\spring-aspects\5.2.15.RELEASE\spring-aspects-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\com\h2database\h2\1.4.200\h2-1.4.200.jar;D:\env\dyt_mavenRepository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;D:\env\dyt_mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.0\mybatis-spring-boot-starter-2.2.0.jar;D:\env\dyt_mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.0\mybatis-spring-boot-autoconfigure-2.2.0.jar;D:\env\dyt_mavenRepository\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;D:\env\dyt_mavenRepository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-test\2.3.12.RELEASE\spring-boot-starter-test-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-test\2.3.12.RELEASE\spring-boot-test-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.12.RELEASE\spring-boot-test-autoconfigure-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;D:\env\dyt_mavenRepository\net\minidev\json-smart\2.3.1\json-smart-2.3.1.jar;D:\env\dyt_mavenRepository\net\minidev\accessors-smart\2.3.1\accessors-smart-2.3.1.jar;D:\env\dyt_mavenRepository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;D:\env\dyt_mavenRepository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\env\dyt_mavenRepository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\env\dyt_mavenRepository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;D:\env\dyt_mavenRepository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;D:\env\dyt_mavenRepository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\env\dyt_mavenRepository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;D:\env\dyt_mavenRepository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;D:\env\dyt_mavenRepository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;D:\env\dyt_mavenRepository\junit\junit\4.13.2\junit-4.13.2.jar;D:\env\dyt_mavenRepository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;D:\env\dyt_mavenRepository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;D:\env\dyt_mavenRepository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;D:\env\dyt_mavenRepository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;D:\env\dyt_mavenRepository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\env\dyt_mavenRepository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\env\dyt_mavenRepository\org\springframework\spring-core\5.2.15.RELEASE\spring-core-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-jcl\5.2.15.RELEASE\spring-jcl-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-test\5.2.15.RELEASE\spring-test-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;D:\env\dyt_mavenRepository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-validation\2.3.12.RELEASE\spring-boot-starter-validation-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;D:\env\dyt_mavenRepository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;D:\env\dyt_mavenRepository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcpkix-jdk15on\1.70\bcpkix-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcutil-jdk15on\1.70\bcutil-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-cache\2.3.12.RELEASE\spring-boot-starter-cache-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-context-support\5.2.15.RELEASE\spring-context-support-5.2.15.RELEASE.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 11"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\env\Java\jdk1.8.0_341\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire8026041158050070090\surefirebooter7434763189102999629.jar C:\Users\<USER>\AppData\Local\Temp\surefire8026041158050070090 2025-06-24T15-37-51_630-jvmRun1 surefire8351716156232735686tmp surefire_06871122929574736976tmp"/>
    <property name="surefire.test.class.path" value="D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption\target\test-classes;D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption\target\classes;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-web\2.3.12.RELEASE\spring-boot-starter-web-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter\2.3.12.RELEASE\spring-boot-starter-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot\2.3.12.RELEASE\spring-boot-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-autoconfigure\2.3.12.RELEASE\spring-boot-autoconfigure-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-logging\2.3.12.RELEASE\spring-boot-starter-logging-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\env\dyt_mavenRepository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\env\dyt_mavenRepository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;D:\env\dyt_mavenRepository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;D:\env\dyt_mavenRepository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;D:\env\dyt_mavenRepository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\env\dyt_mavenRepository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-json\2.3.12.RELEASE\spring-boot-starter-json-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-databind\2.11.4\jackson-databind-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-annotations\2.11.4\jackson-annotations-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-core\2.11.4\jackson-core-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-tomcat\2.3.12.RELEASE\spring-boot-starter-tomcat-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\apache\tomcat\embed\tomcat-embed-core\9.0.46\tomcat-embed-core-9.0.46.jar;D:\env\dyt_mavenRepository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.46\tomcat-embed-websocket-9.0.46.jar;D:\env\dyt_mavenRepository\org\springframework\spring-web\5.2.15.RELEASE\spring-web-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-beans\5.2.15.RELEASE\spring-beans-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-webmvc\5.2.15.RELEASE\spring-webmvc-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-aop\5.2.15.RELEASE\spring-aop-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-context\5.2.15.RELEASE\spring-context-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-expression\5.2.15.RELEASE\spring-expression-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-data-jpa\2.3.12.RELEASE\spring-boot-starter-data-jpa-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-aop\2.3.12.RELEASE\spring-boot-starter-aop-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-jdbc\2.3.12.RELEASE\spring-boot-starter-jdbc-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;D:\env\dyt_mavenRepository\org\springframework\spring-jdbc\5.2.15.RELEASE\spring-jdbc-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;D:\env\dyt_mavenRepository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;D:\env\dyt_mavenRepository\org\hibernate\hibernate-core\5.4.32.Final\hibernate-core-5.4.32.Final.jar;D:\env\dyt_mavenRepository\org\jboss\logging\jboss-logging\3.4.2.Final\jboss-logging-3.4.2.Final.jar;D:\env\dyt_mavenRepository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;D:\env\dyt_mavenRepository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;D:\env\dyt_mavenRepository\antlr\antlr\2.7.7\antlr-2.7.7.jar;D:\env\dyt_mavenRepository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;D:\env\dyt_mavenRepository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\env\dyt_mavenRepository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;D:\env\dyt_mavenRepository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;D:\env\dyt_mavenRepository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;D:\env\dyt_mavenRepository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;D:\env\dyt_mavenRepository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;D:\env\dyt_mavenRepository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\env\dyt_mavenRepository\org\springframework\data\spring-data-jpa\2.3.9.RELEASE\spring-data-jpa-2.3.9.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\data\spring-data-commons\2.3.9.RELEASE\spring-data-commons-2.3.9.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-orm\5.2.15.RELEASE\spring-orm-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-tx\5.2.15.RELEASE\spring-tx-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;D:\env\dyt_mavenRepository\org\springframework\spring-aspects\5.2.15.RELEASE\spring-aspects-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\com\h2database\h2\1.4.200\h2-1.4.200.jar;D:\env\dyt_mavenRepository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;D:\env\dyt_mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.0\mybatis-spring-boot-starter-2.2.0.jar;D:\env\dyt_mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.0\mybatis-spring-boot-autoconfigure-2.2.0.jar;D:\env\dyt_mavenRepository\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;D:\env\dyt_mavenRepository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-test\2.3.12.RELEASE\spring-boot-starter-test-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-test\2.3.12.RELEASE\spring-boot-test-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.12.RELEASE\spring-boot-test-autoconfigure-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;D:\env\dyt_mavenRepository\net\minidev\json-smart\2.3.1\json-smart-2.3.1.jar;D:\env\dyt_mavenRepository\net\minidev\accessors-smart\2.3.1\accessors-smart-2.3.1.jar;D:\env\dyt_mavenRepository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;D:\env\dyt_mavenRepository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\env\dyt_mavenRepository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\env\dyt_mavenRepository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;D:\env\dyt_mavenRepository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;D:\env\dyt_mavenRepository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\env\dyt_mavenRepository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;D:\env\dyt_mavenRepository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;D:\env\dyt_mavenRepository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;D:\env\dyt_mavenRepository\junit\junit\4.13.2\junit-4.13.2.jar;D:\env\dyt_mavenRepository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;D:\env\dyt_mavenRepository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;D:\env\dyt_mavenRepository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;D:\env\dyt_mavenRepository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;D:\env\dyt_mavenRepository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\env\dyt_mavenRepository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\env\dyt_mavenRepository\org\springframework\spring-core\5.2.15.RELEASE\spring-core-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-jcl\5.2.15.RELEASE\spring-jcl-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-test\5.2.15.RELEASE\spring-test-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;D:\env\dyt_mavenRepository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-validation\2.3.12.RELEASE\spring-boot-starter-validation-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;D:\env\dyt_mavenRepository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;D:\env\dyt_mavenRepository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcpkix-jdk15on\1.70\bcpkix-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcutil-jdk15on\1.70\bcutil-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-cache\2.3.12.RELEASE\spring-boot-starter-cache-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-context-support\5.2.15.RELEASE\spring-context-support-5.2.15.RELEASE.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\env\Java\jdk1.8.0_341\jre"/>
    <property name="basedir" value="D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire8026041158050070090\surefirebooter7434763189102999629.jar"/>
    <property name="sun.boot.class.path" value="D:\env\Java\jdk1.8.0_341\jre\lib\resources.jar;D:\env\Java\jdk1.8.0_341\jre\lib\rt.jar;D:\env\Java\jdk1.8.0_341\jre\lib\jsse.jar;D:\env\Java\jdk1.8.0_341\jre\lib\jce.jar;D:\env\Java\jdk1.8.0_341\jre\lib\charsets.jar;D:\env\Java\jdk1.8.0_341\jre\lib\jfr.jar;D:\env\Java\jdk1.8.0_341\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_341-b10"/>
    <property name="user.name" value="23573"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\env\Java\jdk1.8.0_341\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\env\dyt_mavenRepository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_341"/>
    <property name="user.dir" value="D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\env\Java\jdk1.8.0_341\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Python312\Scripts\;C:\Python312\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\env\Java\jdk1.8.0_341\bin;C:\ProgramData\chocolatey\bin;D:\env\gradle-7.6.2\bin;D:\env\php-8.2.18-nts-Win32-vs16-x64;C:\composer;D:\env\php-8.2.18-nts-Win32-vs16-x64;D:\env;dependency-check-10.0.2-release\dependency-check\bin;D:\env\apache-maven-3.6.3\bin;D:\software\Git\cmd;C:\Program Files\dotnet\;C:\Program Files (x86)\Bitvise SSH Client;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\nodejs\node_global\;C:\Program Files (x86)\Microsoft SQL Server\90\Tools\binn\;D:\env\kubernetes-node-windows-amd64\kubernetes\node\bin;C:\Program Files (x86)\NetSarang\Xftp 8\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\NetSarang\Xshell 8\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\software\Microsoft VS Code\bin;D:\software\IntelliJ IDEA 2023.3.2\bin;D:\software\Fiddler;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Program Files\JetBrains\Writerside 241.18775.98\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\.jetbrains\helm;C:\Users\<USER>\.jetbrains\kubectl;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.341-b10"/>
    <property name="java.ext.dirs" value="D:\env\Java\jdk1.8.0_341\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testDynamicStrategyUpdate" classname="com.example.encryption.DynamicStrategyBasicTest" time="0.114"/>
  <testcase name="testGlobalStrategyOverride" classname="com.example.encryption.DynamicStrategyBasicTest" time="0.098"/>
  <testcase name="testStrategyBasedEncryption" classname="com.example.encryption.DynamicStrategyBasicTest" time="0.065"/>
  <testcase name="testDynamicStrategyService" classname="com.example.encryption.DynamicStrategyBasicTest" time="0.063"/>
  <testcase name="testAlgorithmTesting" classname="com.example.encryption.DynamicStrategyBasicTest" time="0.079"/>
  <testcase name="testAESEncryption" classname="com.example.encryption.DynamicStrategyBasicTest" time="0.071"/>
  <testcase name="testAlgorithmSelection" classname="com.example.encryption.DynamicStrategyBasicTest" time="0.085"/>
  <testcase name="testStrategyStatistics" classname="com.example.encryption.DynamicStrategyBasicTest" time="0.076"/>
</testsuite>