package com.example.encryption.entity;

import com.example.encryption.annotation.EncryptField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 加密策略配置实体类
 * 用于动态存储和管理加密策略配置，支持运行时策略变更
 */
@Entity
@Table(name = "encryption_strategies", 
       uniqueConstraints = @UniqueConstraint(columnNames = "strategy_key"))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EncryptionStrategy {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 策略键 - 对应字段的strategyKey
     */
    @Column(name = "strategy_key", nullable = false, unique = true, length = 100)
    @NotBlank(message = "策略键不能为空")
    private String strategyKey;

    /**
     * 迁移策略
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "migration_strategy", nullable = false, length = 50)
    @NotNull(message = "迁移策略不能为空")
    private EncryptField.MigrationStrategy migrationStrategy;

    /**
     * 加密算法类型
     */
    @Column(name = "algorithm_type", length = 20)
    private String algorithmType = "AES";

    /**
     * 策略描述
     */
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 是否启用
     */
    @Column(name = "enabled", nullable = false)
    private Boolean enabled = true;

    /**
     * 优先级（数值越小优先级越高）
     */
    @Column(name = "priority")
    private Integer priority = 0;

    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    /**
     * 创建者
     */
    @Column(name = "created_by", length = 100)
    private String createdBy;

    /**
     * 更新者
     */
    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    /**
     * 版本号（用于乐观锁）
     */
    @Version
    @Column(name = "version")
    private Long version;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        this.createdAt = now;
        this.updatedAt = now;
        if (this.enabled == null) {
            this.enabled = true;
        }
        if (this.priority == null) {
            this.priority = 0;
        }
        if (this.algorithmType == null) {
            this.algorithmType = "AES";
        }
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 构造函数 - 用于快速创建策略配置
     */
    public EncryptionStrategy(String strategyKey, EncryptField.MigrationStrategy migrationStrategy, String description) {
        this.strategyKey = strategyKey;
        this.migrationStrategy = migrationStrategy;
        this.description = description;
        this.enabled = true;
        this.priority = 0;
        this.algorithmType = "AES";
    }

    /**
     * 构造函数 - 包含算法类型
     */
    public EncryptionStrategy(String strategyKey, EncryptField.MigrationStrategy migrationStrategy, 
                            String algorithmType, String description) {
        this.strategyKey = strategyKey;
        this.migrationStrategy = migrationStrategy;
        this.algorithmType = algorithmType;
        this.description = description;
        this.enabled = true;
        this.priority = 0;
    }

    /**
     * 获取策略摘要信息
     */
    public String getSummary() {
        return String.format("EncryptionStrategy{key='%s', strategy=%s, algorithm='%s', enabled=%s}", 
                           strategyKey, migrationStrategy, algorithmType, enabled);
    }

    /**
     * 检查策略是否有效
     */
    public boolean isValid() {
        return strategyKey != null && !strategyKey.trim().isEmpty() 
               && migrationStrategy != null && enabled != null && enabled;
    }
}
