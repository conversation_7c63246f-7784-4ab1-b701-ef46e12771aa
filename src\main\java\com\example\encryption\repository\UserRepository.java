package com.example.encryption.repository;

import com.example.encryption.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 * 演示加密字段的数据库操作
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户信息
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据用户状态查找用户列表
     * @param status 用户状态
     * @return 用户列表
     */
    List<User> findByStatus(User.UserStatus status);
    
    /**
     * 根据年龄范围查找用户
     * @param minAge 最小年龄
     * @param maxAge 最大年龄
     * @return 用户列表
     */
    @Query("SELECT u FROM User u WHERE u.age BETWEEN :minAge AND :maxAge")
    List<User> findByAgeBetween(@Param("minAge") Integer minAge, @Param("maxAge") Integer maxAge);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 统计活跃用户数量
     * @return 活跃用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.status = 'ACTIVE'")
    long countActiveUsers();
    
    /**
     * 注意：对于加密字段，我们不能直接在数据库层面进行查询
     * 因为数据库中存储的是加密后的数据，无法直接匹配明文
     * 如果需要根据加密字段查询，需要在应用层进行处理
     * 
     * 例如：根据手机号查找用户需要在Service层实现
     * 1. 先加密查询条件
     * 2. 然后在数据库中查找加密后的值
     * 或者
     * 1. 查询所有用户
     * 2. 在应用层解密后进行匹配
     */
}
