# 明文字段清理方案技术文档

## 1. 方案概述

### 1.1 清理目标
在完成渐进式影子字段迁移后，需要清理原有的明文字段，彻底消除数据泄露风险，确保敏感数据仅以加密形式存储。

### 1.2 清理原则
- **安全第一**：确保清理过程不影响业务正常运行
- **分批处理**：按表分批，每次只清理一部分数据
- **可控可停**：支持暂停、恢复、回滚操作
- **监控告警**：全程监控，异常自动告警

### 1.3 技术架构
- **任务调度**：基于xxl-job分布式任务调度平台
- **进度管理**：使用Redis存储清理进度和状态
- **批量处理**：JDBC批量操作，控制事务大小
- **监控体系**：实时监控清理进度和系统性能

## 2. 技术实现

### 2.1 核心组件

#### xxl-job任务处理器
```java
@Component
@Slf4j
public class PlaintextCleanupJobHandler {
    
    @Autowired
    private PlaintextCleanupService cleanupService;
    
    /**
     * xxl-job定时任务：清理明文字段
     * 执行参数格式：tableName=ych_patient,batchSize=100,delayMs=200
     */
    @XxlJob("plaintextCleanupJob")
    public void plaintextCleanupJob() {
        String param = XxlJobHelper.getJobParam();
        log.info("开始执行明文字段清理任务，参数：{}", param);
        
        try {
            // 解析任务参数
            Map<String, String> params = parseJobParams(param);
            String tableName = params.get("tableName");
            int batchSize = Integer.parseInt(params.getOrDefault("batchSize", "100"));
            int delayMs = Integer.parseInt(params.getOrDefault("delayMs", "200"));
            
            // 执行清理任务
            CleanupResult result = cleanupService.cleanupTable(tableName, batchSize, delayMs);
            
            if (result.isCompleted()) {
                XxlJobHelper.handleSuccess("表 " + tableName + " 清理完成，共处理 " + result.getProcessedCount() + " 条记录");
            } else {
                XxlJobHelper.handleSuccess("表 " + tableName + " 本批次处理完成，已处理 " + result.getProcessedCount() + " 条记录");
            }
            
        } catch (Exception e) {
            log.error("明文字段清理任务执行失败", e);
            XxlJobHelper.handleFail("清理任务执行失败：" + e.getMessage());
        }
    }
    
    /**
     * 解析任务参数
     */
    private Map<String, String> parseJobParams(String param) {
        Map<String, String> params = new HashMap<>();
        if (StringUtils.hasText(param)) {
            String[] pairs = param.split(",");
            for (String pair : pairs) {
                String[] kv = pair.split("=");
                if (kv.length == 2) {
                    params.put(kv[0].trim(), kv[1].trim());
                }
            }
        }
        return params;
    }
}
```

#### 清理服务核心逻辑
```java
@Service
@Slf4j
public class PlaintextCleanupService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String CLEANUP_STATUS_KEY = "cleanup:status:";
    private static final String CLEANUP_PROGRESS_KEY = "cleanup:progress:";
    private static final String CLEANUP_BACKUP_KEY = "cleanup:backup:";
    
    /**
     * 清理指定表的明文字段
     */
    @Transactional
    public CleanupResult cleanupTable(String tableName, int batchSize, int delayMs) {
        // 检查清理状态
        String statusKey = CLEANUP_STATUS_KEY + tableName;
        String status = (String) redisTemplate.opsForValue().get(statusKey);
        
        if ("PAUSED".equals(status)) {
            log.info("表 {} 的清理任务已暂停", tableName);
            return CleanupResult.paused();
        }
        
        // 获取清理进度
        String progressKey = CLEANUP_PROGRESS_KEY + tableName;
        Long lastProcessedId = (Long) redisTemplate.opsForValue().get(progressKey);
        if (lastProcessedId == null) {
            lastProcessedId = 0L;
        }
        
        // 根据表名选择清理策略
        switch (tableName) {
            case "ych_patient":
                return cleanupPatientTable(lastProcessedId, batchSize, delayMs);
            case "ych_user":
                return cleanupUserTable(lastProcessedId, batchSize, delayMs);
            case "ych_appoint_record":
                return cleanupAppointRecordTable(lastProcessedId, batchSize, delayMs);
            default:
                throw new IllegalArgumentException("不支持的表名：" + tableName);
        }
    }
    
    /**
     * 清理患者表明文字段
     */
    private CleanupResult cleanupPatientTable(Long lastProcessedId, int batchSize, int delayMs) {
        String tableName = "ych_patient";
        
        // 查询需要清理的记录
        String selectSql = "SELECT id, name, phone, id_card_no, address FROM ych_patient " +
                          "WHERE id > ? AND migration_status = 'COMPLETED' " +
                          "AND (name IS NOT NULL OR phone IS NOT NULL OR id_card_no IS NOT NULL) " +
                          "ORDER BY id LIMIT ?";
        
        List<Map<String, Object>> records = jdbcTemplate.queryForList(selectSql, lastProcessedId, batchSize);
        
        if (records.isEmpty()) {
            // 清理完成
            redisTemplate.delete(CLEANUP_PROGRESS_KEY + tableName);
            redisTemplate.delete(CLEANUP_STATUS_KEY + tableName);
            log.info("表 {} 明文字段清理完成", tableName);
            return CleanupResult.completed(0);
        }
        
        // 备份明文数据（用于紧急回滚）
        backupPlaintextData(tableName, records);
        
        // 批量清理明文字段
        List<Long> ids = records.stream()
                .map(record -> (Long) record.get("id"))
                .collect(Collectors.toList());
        
        String updateSql = "UPDATE ych_patient SET " +
                          "name = NULL, " +
                          "phone = NULL, " +
                          "id_card_no = NULL, " +
                          "address = NULL, " +
                          "cleanup_time = NOW() " +
                          "WHERE id IN (" + String.join(",", Collections.nCopies(ids.size(), "?")) + ")";
        
        Object[] params = ids.toArray();
        int updatedCount = jdbcTemplate.update(updateSql, params);
        
        // 更新进度
        Long maxId = ids.get(ids.size() - 1);
        redisTemplate.opsForValue().set(CLEANUP_PROGRESS_KEY + tableName, maxId);
        
        // 控制清理速度
        if (delayMs > 0) {
            try {
                Thread.sleep(delayMs);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("表 {} 本批次清理完成，处理记录数：{}，最大ID：{}", tableName, updatedCount, maxId);
        return CleanupResult.processing(updatedCount);
    }
    
    /**
     * 备份明文数据
     */
    private void backupPlaintextData(String tableName, List<Map<String, Object>> records) {
        String backupKey = CLEANUP_BACKUP_KEY + tableName + ":" + System.currentTimeMillis();
        redisTemplate.opsForValue().set(backupKey, records, Duration.ofDays(7)); // 保留7天
    }
    
    /**
     * 暂停清理任务
     */
    public void pauseCleanup(String tableName) {
        redisTemplate.opsForValue().set(CLEANUP_STATUS_KEY + tableName, "PAUSED");
        log.info("表 {} 的清理任务已暂停", tableName);
    }
    
    /**
     * 恢复清理任务
     */
    public void resumeCleanup(String tableName) {
        redisTemplate.delete(CLEANUP_STATUS_KEY + tableName);
        log.info("表 {} 的清理任务已恢复", tableName);
    }
    
    /**
     * 获取清理进度
     */
    public CleanupProgress getCleanupProgress(String tableName) {
        Long lastProcessedId = (Long) redisTemplate.opsForValue().get(CLEANUP_PROGRESS_KEY + tableName);
        String status = (String) redisTemplate.opsForValue().get(CLEANUP_STATUS_KEY + tableName);
        
        // 计算总记录数和已处理记录数
        String totalSql = "SELECT COUNT(*) FROM " + tableName + " WHERE migration_status = 'COMPLETED'";
        Long totalCount = jdbcTemplate.queryForObject(totalSql, Long.class);
        
        String processedSql = "SELECT COUNT(*) FROM " + tableName + 
                            " WHERE migration_status = 'COMPLETED' AND cleanup_time IS NOT NULL";
        Long processedCount = jdbcTemplate.queryForObject(processedSql, Long.class);
        
        return CleanupProgress.builder()
                .tableName(tableName)
                .totalCount(totalCount)
                .processedCount(processedCount)
                .lastProcessedId(lastProcessedId)
                .status(status != null ? status : "RUNNING")
                .progressPercent(totalCount > 0 ? (processedCount * 100.0 / totalCount) : 0)
                .build();
    }
}
```

### 2.2 数据模型

#### 清理结果模型
```java
@Data
@Builder
public class CleanupResult {
    private boolean completed;
    private boolean paused;
    private int processedCount;
    private String message;
    
    public static CleanupResult completed(int count) {
        return CleanupResult.builder()
                .completed(true)
                .processedCount(count)
                .message("清理完成")
                .build();
    }
    
    public static CleanupResult processing(int count) {
        return CleanupResult.builder()
                .completed(false)
                .processedCount(count)
                .message("处理中")
                .build();
    }
    
    public static CleanupResult paused() {
        return CleanupResult.builder()
                .paused(true)
                .message("已暂停")
                .build();
    }
}
```

#### 清理进度模型
```java
@Data
@Builder
public class CleanupProgress {
    private String tableName;
    private Long totalCount;
    private Long processedCount;
    private Long lastProcessedId;
    private String status;
    private Double progressPercent;
    private LocalDateTime lastUpdateTime;
}
```

## 3. 配置管理

### 3.1 xxl-job配置
```yaml
# application.yml
xxl:
  job:
    admin:
      addresses: http://xxl-job-admin:8080/xxl-job-admin
    executor:
      appname: dyt-data-encryption
      address: 
      ip: 
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
```

### 3.2 清理任务配置
```bash
# 在xxl-job管理后台配置以下任务：

# 任务1：清理患者表
任务名称：患者表明文字段清理
Cron表达式：0 */10 * * * ?  # 每10分钟执行一次
执行器：dyt-data-encryption
JobHandler：plaintextCleanupJob
执行参数：tableName=ych_patient,batchSize=100,delayMs=200
路由策略：第一个
阻塞处理策略：单机串行
任务超时时间：300秒
失败重试次数：3

# 任务2：清理用户表
任务名称：用户表明文字段清理
Cron表达式：0 5 */1 * * ?   # 每小时的第5分钟执行
执行器：dyt-data-encryption
JobHandler：plaintextCleanupJob
执行参数：tableName=ych_user,batchSize=50,delayMs=300
路由策略：第一个
阻塞处理策略：单机串行
任务超时时间：300秒
失败重试次数：3

# 任务3：清理预约记录表
任务名称：预约记录表明文字段清理
Cron表达式：0 15 */2 * * ?  # 每2小时的第15分钟执行
执行器：dyt-data-encryption
JobHandler：plaintextCleanupJob
执行参数：tableName=ych_appoint_record,batchSize=200,delayMs=100
路由策略：第一个
阻塞处理策略：单机串行
任务超时时间：300秒
失败重试次数：3
```

## 4. 监控与管理

### 4.1 清理监控API
```java
@RestController
@RequestMapping("/api/migration/cleanup")
@Slf4j
public class CleanupController {
    
    @Autowired
    private PlaintextCleanupService cleanupService;
    
    /**
     * 启动清理任务
     */
    @PostMapping("/start")
    public ResponseEntity<String> startCleanup(@RequestParam String tableName) {
        try {
            // 通过xxl-job API启动任务
            return ResponseEntity.ok("清理任务已启动：" + tableName);
        } catch (Exception e) {
            return ResponseEntity.status(500).body("启动清理任务失败：" + e.getMessage());
        }
    }
    
    /**
     * 暂停清理任务
     */
    @PostMapping("/pause")
    public ResponseEntity<String> pauseCleanup(@RequestParam String tableName) {
        cleanupService.pauseCleanup(tableName);
        return ResponseEntity.ok("清理任务已暂停：" + tableName);
    }
    
    /**
     * 恢复清理任务
     */
    @PostMapping("/resume")
    public ResponseEntity<String> resumeCleanup(@RequestParam String tableName) {
        cleanupService.resumeCleanup(tableName);
        return ResponseEntity.ok("清理任务已恢复：" + tableName);
    }
    
    /**
     * 查看清理进度
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, CleanupProgress>> getCleanupStatus() {
        Map<String, CleanupProgress> statusMap = new HashMap<>();
        statusMap.put("ych_patient", cleanupService.getCleanupProgress("ych_patient"));
        statusMap.put("ych_user", cleanupService.getCleanupProgress("ych_user"));
        statusMap.put("ych_appoint_record", cleanupService.getCleanupProgress("ych_appoint_record"));
        return ResponseEntity.ok(statusMap);
    }
    
    /**
     * 获取单表清理进度
     */
    @GetMapping("/status/{tableName}")
    public ResponseEntity<CleanupProgress> getTableCleanupStatus(@PathVariable String tableName) {
        CleanupProgress progress = cleanupService.getCleanupProgress(tableName);
        return ResponseEntity.ok(progress);
    }
}
```

### 4.2 监控指标
- **清理进度**：已处理记录数/总记录数
- **清理速度**：每分钟处理记录数
- **任务状态**：运行中/暂停/完成/失败
- **系统性能**：CPU使用率、内存使用率、数据库连接数
- **错误率**：失败任务数/总任务数

## 5. 安全措施

### 5.1 数据备份
- **Redis备份**：清理前将明文数据备份到Redis，保留7天
- **数据库备份**：定期全量备份，支持快速恢复
- **操作日志**：记录所有清理操作，便于审计

### 5.2 回滚机制
- **快速回滚**：从Redis备份恢复最近清理的数据
- **完整回滚**：从数据库备份恢复到清理前状态
- **部分回滚**：根据ID范围恢复特定数据

### 5.3 权限控制
- **操作权限**：只有管理员可以启动/暂停清理任务
- **API权限**：清理API需要特殊权限验证
- **审计日志**：记录所有操作人员和操作时间

## 6. 实施计划

### 6.1 准备阶段（1天）
1. **环境配置**：部署xxl-job调度中心
2. **代码部署**：部署清理组件到生产环境
3. **任务配置**：在xxl-job后台配置清理任务

### 6.2 执行阶段（1-2周）
1. **核心表优先**：先清理患者表、用户表等核心表
2. **分批执行**：每天清理1-2个表，避免集中处理
3. **实时监控**：监控清理进度和系统性能

### 6.3 验证阶段（2-3天）
1. **数据验证**：确认明文字段已清空
2. **功能验证**：确认业务功能正常
3. **性能验证**：确认系统性能无明显下降

## 7. 风险控制

### 7.1 技术风险
- **数据丢失**：通过多重备份机制防范
- **性能影响**：通过分批处理和延时控制
- **任务失败**：通过重试机制和监控告警

### 7.2 业务风险
- **功能异常**：通过充分测试和灰度发布
- **用户体验**：清理过程对用户透明
- **合规风险**：确保清理过程符合数据保护法规

## 总结

明文字段清理方案通过xxl-job分布式任务调度，实现了安全、可控、可监控的明文数据清理。该方案具有以下特点：

1. **安全可靠**：多重备份，支持快速回滚
2. **性能友好**：分批处理，控制系统负载
3. **监控完善**：实时监控，异常告警
4. **操作简便**：API管理，一键启停

通过该方案的实施，可以彻底消除明文数据泄露风险，确保敏感数据的安全性。
