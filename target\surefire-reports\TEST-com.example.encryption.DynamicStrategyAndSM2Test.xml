<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.example.encryption.DynamicStrategyAndSM2Test" time="9.66" tests="8" errors="2" skipped="0" failures="2">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption\target\test-classes;D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption\target\classes;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-web\2.3.12.RELEASE\spring-boot-starter-web-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter\2.3.12.RELEASE\spring-boot-starter-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot\2.3.12.RELEASE\spring-boot-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-autoconfigure\2.3.12.RELEASE\spring-boot-autoconfigure-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-logging\2.3.12.RELEASE\spring-boot-starter-logging-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\env\dyt_mavenRepository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\env\dyt_mavenRepository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;D:\env\dyt_mavenRepository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;D:\env\dyt_mavenRepository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;D:\env\dyt_mavenRepository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\env\dyt_mavenRepository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-json\2.3.12.RELEASE\spring-boot-starter-json-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-databind\2.11.4\jackson-databind-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-annotations\2.11.4\jackson-annotations-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-core\2.11.4\jackson-core-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-tomcat\2.3.12.RELEASE\spring-boot-starter-tomcat-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\apache\tomcat\embed\tomcat-embed-core\9.0.46\tomcat-embed-core-9.0.46.jar;D:\env\dyt_mavenRepository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.46\tomcat-embed-websocket-9.0.46.jar;D:\env\dyt_mavenRepository\org\springframework\spring-web\5.2.15.RELEASE\spring-web-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-beans\5.2.15.RELEASE\spring-beans-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-webmvc\5.2.15.RELEASE\spring-webmvc-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-aop\5.2.15.RELEASE\spring-aop-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-context\5.2.15.RELEASE\spring-context-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-expression\5.2.15.RELEASE\spring-expression-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-data-jpa\2.3.12.RELEASE\spring-boot-starter-data-jpa-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-aop\2.3.12.RELEASE\spring-boot-starter-aop-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-jdbc\2.3.12.RELEASE\spring-boot-starter-jdbc-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;D:\env\dyt_mavenRepository\org\springframework\spring-jdbc\5.2.15.RELEASE\spring-jdbc-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;D:\env\dyt_mavenRepository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;D:\env\dyt_mavenRepository\org\hibernate\hibernate-core\5.4.32.Final\hibernate-core-5.4.32.Final.jar;D:\env\dyt_mavenRepository\org\jboss\logging\jboss-logging\3.4.2.Final\jboss-logging-3.4.2.Final.jar;D:\env\dyt_mavenRepository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;D:\env\dyt_mavenRepository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;D:\env\dyt_mavenRepository\antlr\antlr\2.7.7\antlr-2.7.7.jar;D:\env\dyt_mavenRepository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;D:\env\dyt_mavenRepository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\env\dyt_mavenRepository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;D:\env\dyt_mavenRepository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;D:\env\dyt_mavenRepository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;D:\env\dyt_mavenRepository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;D:\env\dyt_mavenRepository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;D:\env\dyt_mavenRepository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\env\dyt_mavenRepository\org\springframework\data\spring-data-jpa\2.3.9.RELEASE\spring-data-jpa-2.3.9.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\data\spring-data-commons\2.3.9.RELEASE\spring-data-commons-2.3.9.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-orm\5.2.15.RELEASE\spring-orm-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-tx\5.2.15.RELEASE\spring-tx-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;D:\env\dyt_mavenRepository\org\springframework\spring-aspects\5.2.15.RELEASE\spring-aspects-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\com\h2database\h2\1.4.200\h2-1.4.200.jar;D:\env\dyt_mavenRepository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;D:\env\dyt_mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.0\mybatis-spring-boot-starter-2.2.0.jar;D:\env\dyt_mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.0\mybatis-spring-boot-autoconfigure-2.2.0.jar;D:\env\dyt_mavenRepository\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;D:\env\dyt_mavenRepository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-test\2.3.12.RELEASE\spring-boot-starter-test-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-test\2.3.12.RELEASE\spring-boot-test-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.12.RELEASE\spring-boot-test-autoconfigure-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;D:\env\dyt_mavenRepository\net\minidev\json-smart\2.3.1\json-smart-2.3.1.jar;D:\env\dyt_mavenRepository\net\minidev\accessors-smart\2.3.1\accessors-smart-2.3.1.jar;D:\env\dyt_mavenRepository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;D:\env\dyt_mavenRepository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\env\dyt_mavenRepository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\env\dyt_mavenRepository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;D:\env\dyt_mavenRepository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;D:\env\dyt_mavenRepository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\env\dyt_mavenRepository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;D:\env\dyt_mavenRepository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;D:\env\dyt_mavenRepository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;D:\env\dyt_mavenRepository\junit\junit\4.13.2\junit-4.13.2.jar;D:\env\dyt_mavenRepository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;D:\env\dyt_mavenRepository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;D:\env\dyt_mavenRepository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;D:\env\dyt_mavenRepository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;D:\env\dyt_mavenRepository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\env\dyt_mavenRepository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\env\dyt_mavenRepository\org\springframework\spring-core\5.2.15.RELEASE\spring-core-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-jcl\5.2.15.RELEASE\spring-jcl-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-test\5.2.15.RELEASE\spring-test-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;D:\env\dyt_mavenRepository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-validation\2.3.12.RELEASE\spring-boot-starter-validation-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;D:\env\dyt_mavenRepository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;D:\env\dyt_mavenRepository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcpkix-jdk15on\1.70\bcpkix-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcutil-jdk15on\1.70\bcutil-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-cache\2.3.12.RELEASE\spring-boot-starter-cache-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-context-support\5.2.15.RELEASE\spring-context-support-5.2.15.RELEASE.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 11"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\env\Java\jdk1.8.0_341\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire8026041158050070090\surefirebooter7434763189102999629.jar C:\Users\<USER>\AppData\Local\Temp\surefire8026041158050070090 2025-06-24T15-37-51_630-jvmRun1 surefire8351716156232735686tmp surefire_06871122929574736976tmp"/>
    <property name="surefire.test.class.path" value="D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption\target\test-classes;D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption\target\classes;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-web\2.3.12.RELEASE\spring-boot-starter-web-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter\2.3.12.RELEASE\spring-boot-starter-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot\2.3.12.RELEASE\spring-boot-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-autoconfigure\2.3.12.RELEASE\spring-boot-autoconfigure-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-logging\2.3.12.RELEASE\spring-boot-starter-logging-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\env\dyt_mavenRepository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\env\dyt_mavenRepository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;D:\env\dyt_mavenRepository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;D:\env\dyt_mavenRepository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;D:\env\dyt_mavenRepository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\env\dyt_mavenRepository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-json\2.3.12.RELEASE\spring-boot-starter-json-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-databind\2.11.4\jackson-databind-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-annotations\2.11.4\jackson-annotations-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\core\jackson-core\2.11.4\jackson-core-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;D:\env\dyt_mavenRepository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-tomcat\2.3.12.RELEASE\spring-boot-starter-tomcat-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\apache\tomcat\embed\tomcat-embed-core\9.0.46\tomcat-embed-core-9.0.46.jar;D:\env\dyt_mavenRepository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.46\tomcat-embed-websocket-9.0.46.jar;D:\env\dyt_mavenRepository\org\springframework\spring-web\5.2.15.RELEASE\spring-web-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-beans\5.2.15.RELEASE\spring-beans-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-webmvc\5.2.15.RELEASE\spring-webmvc-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-aop\5.2.15.RELEASE\spring-aop-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-context\5.2.15.RELEASE\spring-context-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-expression\5.2.15.RELEASE\spring-expression-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-data-jpa\2.3.12.RELEASE\spring-boot-starter-data-jpa-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-aop\2.3.12.RELEASE\spring-boot-starter-aop-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-jdbc\2.3.12.RELEASE\spring-boot-starter-jdbc-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;D:\env\dyt_mavenRepository\org\springframework\spring-jdbc\5.2.15.RELEASE\spring-jdbc-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\jakarta\transaction\jakarta.transaction-api\1.3.3\jakarta.transaction-api-1.3.3.jar;D:\env\dyt_mavenRepository\jakarta\persistence\jakarta.persistence-api\2.2.3\jakarta.persistence-api-2.2.3.jar;D:\env\dyt_mavenRepository\org\hibernate\hibernate-core\5.4.32.Final\hibernate-core-5.4.32.Final.jar;D:\env\dyt_mavenRepository\org\jboss\logging\jboss-logging\3.4.2.Final\jboss-logging-3.4.2.Final.jar;D:\env\dyt_mavenRepository\org\javassist\javassist\3.27.0-GA\javassist-3.27.0-GA.jar;D:\env\dyt_mavenRepository\net\bytebuddy\byte-buddy\1.10.22\byte-buddy-1.10.22.jar;D:\env\dyt_mavenRepository\antlr\antlr\2.7.7\antlr-2.7.7.jar;D:\env\dyt_mavenRepository\org\jboss\jandex\2.2.3.Final\jandex-2.2.3.Final.jar;D:\env\dyt_mavenRepository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\env\dyt_mavenRepository\org\dom4j\dom4j\2.1.3\dom4j-2.1.3.jar;D:\env\dyt_mavenRepository\org\hibernate\common\hibernate-commons-annotations\5.1.2.Final\hibernate-commons-annotations-5.1.2.Final.jar;D:\env\dyt_mavenRepository\org\glassfish\jaxb\jaxb-runtime\2.3.4\jaxb-runtime-2.3.4.jar;D:\env\dyt_mavenRepository\org\glassfish\jaxb\txw2\2.3.4\txw2-2.3.4.jar;D:\env\dyt_mavenRepository\com\sun\istack\istack-commons-runtime\3.0.12\istack-commons-runtime-3.0.12.jar;D:\env\dyt_mavenRepository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\env\dyt_mavenRepository\org\springframework\data\spring-data-jpa\2.3.9.RELEASE\spring-data-jpa-2.3.9.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\data\spring-data-commons\2.3.9.RELEASE\spring-data-commons-2.3.9.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-orm\5.2.15.RELEASE\spring-orm-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-tx\5.2.15.RELEASE\spring-tx-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;D:\env\dyt_mavenRepository\org\springframework\spring-aspects\5.2.15.RELEASE\spring-aspects-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\com\h2database\h2\1.4.200\h2-1.4.200.jar;D:\env\dyt_mavenRepository\mysql\mysql-connector-java\8.0.25\mysql-connector-java-8.0.25.jar;D:\env\dyt_mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.0\mybatis-spring-boot-starter-2.2.0.jar;D:\env\dyt_mavenRepository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.0\mybatis-spring-boot-autoconfigure-2.2.0.jar;D:\env\dyt_mavenRepository\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;D:\env\dyt_mavenRepository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-test\2.3.12.RELEASE\spring-boot-starter-test-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-test\2.3.12.RELEASE\spring-boot-test-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.12.RELEASE\spring-boot-test-autoconfigure-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;D:\env\dyt_mavenRepository\net\minidev\json-smart\2.3.1\json-smart-2.3.1.jar;D:\env\dyt_mavenRepository\net\minidev\accessors-smart\2.3.1\accessors-smart-2.3.1.jar;D:\env\dyt_mavenRepository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;D:\env\dyt_mavenRepository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\env\dyt_mavenRepository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\env\dyt_mavenRepository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;D:\env\dyt_mavenRepository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;D:\env\dyt_mavenRepository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\env\dyt_mavenRepository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;D:\env\dyt_mavenRepository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;D:\env\dyt_mavenRepository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;D:\env\dyt_mavenRepository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;D:\env\dyt_mavenRepository\junit\junit\4.13.2\junit-4.13.2.jar;D:\env\dyt_mavenRepository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;D:\env\dyt_mavenRepository\net\bytebuddy\byte-buddy-agent\1.10.22\byte-buddy-agent-1.10.22.jar;D:\env\dyt_mavenRepository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;D:\env\dyt_mavenRepository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;D:\env\dyt_mavenRepository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\env\dyt_mavenRepository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\env\dyt_mavenRepository\org\springframework\spring-core\5.2.15.RELEASE\spring-core-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-jcl\5.2.15.RELEASE\spring-jcl-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-test\5.2.15.RELEASE\spring-test-5.2.15.RELEASE.jar;D:\env\dyt_mavenRepository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;D:\env\dyt_mavenRepository\org\projectlombok\lombok\1.18.20\lombok-1.18.20.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-validation\2.3.12.RELEASE\spring-boot-starter-validation-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;D:\env\dyt_mavenRepository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;D:\env\dyt_mavenRepository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcprov-jdk15on\1.70\bcprov-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcpkix-jdk15on\1.70\bcpkix-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\bouncycastle\bcutil-jdk15on\1.70\bcutil-jdk15on-1.70.jar;D:\env\dyt_mavenRepository\org\springframework\boot\spring-boot-starter-cache\2.3.12.RELEASE\spring-boot-starter-cache-2.3.12.RELEASE.jar;D:\env\dyt_mavenRepository\org\springframework\spring-context-support\5.2.15.RELEASE\spring-context-support-5.2.15.RELEASE.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\env\Java\jdk1.8.0_341\jre"/>
    <property name="basedir" value="D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire8026041158050070090\surefirebooter7434763189102999629.jar"/>
    <property name="sun.boot.class.path" value="D:\env\Java\jdk1.8.0_341\jre\lib\resources.jar;D:\env\Java\jdk1.8.0_341\jre\lib\rt.jar;D:\env\Java\jdk1.8.0_341\jre\lib\jsse.jar;D:\env\Java\jdk1.8.0_341\jre\lib\jce.jar;D:\env\Java\jdk1.8.0_341\jre\lib\charsets.jar;D:\env\Java\jdk1.8.0_341\jre\lib\jfr.jar;D:\env\Java\jdk1.8.0_341\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_341-b10"/>
    <property name="user.name" value="23573"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\env\Java\jdk1.8.0_341\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\env\dyt_mavenRepository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_341"/>
    <property name="user.dir" value="D:\workspace\DYT\projectCode\hwc\dyt-data-encryption-decryption"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\env\Java\jdk1.8.0_341\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;C:\Python312\Scripts\;C:\Python312\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\env\Java\jdk1.8.0_341\bin;C:\ProgramData\chocolatey\bin;D:\env\gradle-7.6.2\bin;D:\env\php-8.2.18-nts-Win32-vs16-x64;C:\composer;D:\env\php-8.2.18-nts-Win32-vs16-x64;D:\env;dependency-check-10.0.2-release\dependency-check\bin;D:\env\apache-maven-3.6.3\bin;D:\software\Git\cmd;C:\Program Files\dotnet\;C:\Program Files (x86)\Bitvise SSH Client;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Program Files\nodejs\node_global\;C:\Program Files (x86)\Microsoft SQL Server\90\Tools\binn\;D:\env\kubernetes-node-windows-amd64\kubernetes\node\bin;C:\Program Files (x86)\NetSarang\Xftp 8\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files (x86)\NetSarang\Xshell 8\;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\software\Microsoft VS Code\bin;D:\software\IntelliJ IDEA 2023.3.2\bin;D:\software\Fiddler;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Program Files\JetBrains\Writerside 241.18775.98\bin;C:\Users\<USER>\AppData\Roaming\nvm;C:\Program Files\nodejs;C:\Users\<USER>\.lmstudio\bin;C:\Users\<USER>\.jetbrains\helm;C:\Users\<USER>\.jetbrains\kubectl;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.341-b10"/>
    <property name="java.ext.dirs" value="D:\env\Java\jdk1.8.0_341\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testDynamicStrategyUpdate" classname="com.example.encryption.DynamicStrategyAndSM2Test" time="0.212"/>
  <testcase name="testGlobalStrategyOverride" classname="com.example.encryption.DynamicStrategyAndSM2Test" time="0.123"/>
  <testcase name="testStrategyBasedEncryption" classname="com.example.encryption.DynamicStrategyAndSM2Test" time="0.469"/>
  <testcase name="testEncryptionUtilWithDifferentAlgorithms" classname="com.example.encryption.DynamicStrategyAndSM2Test" time="1.203">
    <error message="加密失败: SM2加密失败" type="java.lang.RuntimeException">java.lang.RuntimeException: 加密失败: SM2加密失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
Caused by: java.lang.RuntimeException: SM2加密失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
Caused by: java.lang.RuntimeException: 解析SM2公钥失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
Caused by: org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63)
</error>
    <system-out><![CDATA[2025-06-24 15:38:00.612  INFO 53772 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@5db6b9cd testClass = DynamicStrategyAndSM2Test, testInstance = com.example.encryption.DynamicStrategyAndSM2Test@798b36fd, testMethod = testEncryptionUtilWithDifferentAlgorithms@DynamicStrategyAndSM2Test, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@210ab13f testClass = DynamicStrategyAndSM2Test, locations = '{}', classes = '{class com.example.encryption.EncryptionDemoApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@55141def, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@235834f2, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@6b2ea799, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@22ff4249, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@604ed9f0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true]]; transaction manager [org.springframework.orm.jpa.JpaTransactionManager@20faaf77]; rollback [true]
=== 测试统一加密工具类 ===
AES: 测试数据：13812345678 -> Y5Y7chz25TuxFcNiYuRbURYijON669p8rWbJTlJ6powFBnCTxX78KvEmR2bTSGPJBSReHusb -> 测试数据：13812345678
2025-06-24 15:38:00.991 ERROR 53772 --- [           main] com.example.encryption.util.SM2Util      : 解析SM2公钥失败: 048356e642a40ebd18d29ba3532fbd9f3bbee8f027c3f6f39a5ba2f870369f9988981f5efe55d1f06d3e9b7b8e7a8b5c6d4e3f2a1b9c8d7e6f5a4b3c2d1e0f9e8

org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at org.bouncycastle.util.encoders.Hex.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	at com.example.encryption.util.SM2Util.parsePublicKey(SM2Util.java:191) [classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:114) [classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:95) [classes/:na]
	at com.example.encryption.util.EncryptionUtil.encrypt(EncryptionUtil.java:61) [classes/:na]
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63) [test-classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_341]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_341]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_341]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_341]
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686) [junit-platform-commons-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418) ~[surefire-booter-2.22.2.jar:2.22.2]
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at java.lang.String.charAt(String.java:658) ~[na:1.8.0_341]
	at org.bouncycastle.util.encoders.HexEncoder.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	... 71 common frames omitted

2025-06-24 15:38:00.992 ERROR 53772 --- [           main] com.example.encryption.util.SM2Util      : SM2加密失败

java.lang.RuntimeException: 解析SM2公钥失败
	at com.example.encryption.util.SM2Util.parsePublicKey(SM2Util.java:196) [classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:114) [classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:95) [classes/:na]
	at com.example.encryption.util.EncryptionUtil.encrypt(EncryptionUtil.java:61) [classes/:na]
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63) [test-classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_341]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_341]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_341]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_341]
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686) [junit-platform-commons-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418) ~[surefire-booter-2.22.2.jar:2.22.2]
Caused by: org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at org.bouncycastle.util.encoders.Hex.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	at com.example.encryption.util.SM2Util.parsePublicKey(SM2Util.java:191) [classes/:na]
	... 69 common frames omitted
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at java.lang.String.charAt(String.java:658) ~[na:1.8.0_341]
	at org.bouncycastle.util.encoders.HexEncoder.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	... 71 common frames omitted

2025-06-24 15:38:00.993 ERROR 53772 --- [           main] c.e.encryption.util.EncryptionUtil       : 使用SM2算法加密失败

java.lang.RuntimeException: SM2加密失败
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:132) ~[classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:95) ~[classes/:na]
	at com.example.encryption.util.EncryptionUtil.encrypt(EncryptionUtil.java:61) ~[classes/:na]
	at com.example.encryption.DynamicStrategyAndSM2Test.testEncryptionUtilWithDifferentAlgorithms(DynamicStrategyAndSM2Test.java:63) [test-classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_341]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_341]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_341]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_341]
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686) [junit-platform-commons-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418) ~[surefire-booter-2.22.2.jar:2.22.2]
Caused by: java.lang.RuntimeException: 解析SM2公钥失败
	at com.example.encryption.util.SM2Util.parsePublicKey(SM2Util.java:196) ~[classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:114) ~[classes/:na]
	... 68 common frames omitted
Caused by: org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at org.bouncycastle.util.encoders.Hex.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	at com.example.encryption.util.SM2Util.parsePublicKey(SM2Util.java:191) ~[classes/:na]
	... 69 common frames omitted
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at java.lang.String.charAt(String.java:658) ~[na:1.8.0_341]
	at org.bouncycastle.util.encoders.HexEncoder.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	... 71 common frames omitted

2025-06-24 15:38:00.999  INFO 53772 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@5db6b9cd testClass = DynamicStrategyAndSM2Test, testInstance = com.example.encryption.DynamicStrategyAndSM2Test@798b36fd, testMethod = testEncryptionUtilWithDifferentAlgorithms@DynamicStrategyAndSM2Test, testException = java.lang.RuntimeException: 加密失败: SM2加密失败, mergedContextConfiguration = [WebMergedContextConfiguration@210ab13f testClass = DynamicStrategyAndSM2Test, locations = '{}', classes = '{class com.example.encryption.EncryptionDemoApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@55141def, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@235834f2, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@6b2ea799, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@22ff4249, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@604ed9f0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true]]
]]></system-out>
  </testcase>
  <testcase name="testDynamicStrategyService" classname="com.example.encryption.DynamicStrategyAndSM2Test" time="0.066">
    <failure message="expected: &lt;SM2&gt; but was: &lt;AES&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: expected: <SM2> but was: <AES>
	at com.example.encryption.DynamicStrategyAndSM2Test.testDynamicStrategyService(DynamicStrategyAndSM2Test.java:91)
]]></failure>
    <system-out><![CDATA[2025-06-24 15:38:01.009  INFO 53772 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@5db6b9cd testClass = DynamicStrategyAndSM2Test, testInstance = com.example.encryption.DynamicStrategyAndSM2Test@6f7b8ae1, testMethod = testDynamicStrategyService@DynamicStrategyAndSM2Test, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@210ab13f testClass = DynamicStrategyAndSM2Test, locations = '{}', classes = '{class com.example.encryption.EncryptionDemoApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@55141def, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@235834f2, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@6b2ea799, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@22ff4249, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@604ed9f0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true]]; transaction manager [org.springframework.orm.jpa.JpaTransactionManager@20faaf77]; rollback [true]
=== 测试动态策略服务 ===
当前策略数量: 5
2025-06-24 15:38:01.014  INFO 53772 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@5db6b9cd testClass = DynamicStrategyAndSM2Test, testInstance = com.example.encryption.DynamicStrategyAndSM2Test@6f7b8ae1, testMethod = testDynamicStrategyService@DynamicStrategyAndSM2Test, testException = org.opentest4j.AssertionFailedError: expected: <SM2> but was: <AES>, mergedContextConfiguration = [WebMergedContextConfiguration@210ab13f testClass = DynamicStrategyAndSM2Test, locations = '{}', classes = '{class com.example.encryption.EncryptionDemoApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@55141def, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@235834f2, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@6b2ea799, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@22ff4249, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@604ed9f0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true]]
]]></system-out>
  </testcase>
  <testcase name="testSM2Encryption" classname="com.example.encryption.DynamicStrategyAndSM2Test" time="0.485">
    <error message="SM2加密失败" type="java.lang.RuntimeException">java.lang.RuntimeException: SM2加密失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37)
Caused by: java.lang.RuntimeException: 解析SM2公钥失败
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37)
Caused by: org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37)
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37)
</error>
    <system-out><![CDATA[2025-06-24 15:38:01.017  INFO 53772 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@5db6b9cd testClass = DynamicStrategyAndSM2Test, testInstance = com.example.encryption.DynamicStrategyAndSM2Test@4be1dce6, testMethod = testSM2Encryption@DynamicStrategyAndSM2Test, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@210ab13f testClass = DynamicStrategyAndSM2Test, locations = '{}', classes = '{class com.example.encryption.EncryptionDemoApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@55141def, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@235834f2, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@6b2ea799, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@22ff4249, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@604ed9f0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true]]; transaction manager [org.springframework.orm.jpa.JpaTransactionManager@20faaf77]; rollback [true]
=== 测试SM2加密算法 ===
原文: 这是一个测试身份证号：110101199001011234
2025-06-24 15:38:01.019 ERROR 53772 --- [           main] com.example.encryption.util.SM2Util      : 解析SM2公钥失败: 048356e642a40ebd18d29ba3532fbd9f3bbee8f027c3f6f39a5ba2f870369f9988981f5efe55d1f06d3e9b7b8e7a8b5c6d4e3f2a1b9c8d7e6f5a4b3c2d1e0f9e8

org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at org.bouncycastle.util.encoders.Hex.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	at com.example.encryption.util.SM2Util.parsePublicKey(SM2Util.java:191) [classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:114) [classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:95) [classes/:na]
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37) [test-classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_341]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_341]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_341]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_341]
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686) [junit-platform-commons-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418) ~[surefire-booter-2.22.2.jar:2.22.2]
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at java.lang.String.charAt(String.java:658) ~[na:1.8.0_341]
	at org.bouncycastle.util.encoders.HexEncoder.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	... 70 common frames omitted

2025-06-24 15:38:01.019 ERROR 53772 --- [           main] com.example.encryption.util.SM2Util      : SM2加密失败

java.lang.RuntimeException: 解析SM2公钥失败
	at com.example.encryption.util.SM2Util.parsePublicKey(SM2Util.java:196) [classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:114) [classes/:na]
	at com.example.encryption.util.SM2Util.encrypt(SM2Util.java:95) [classes/:na]
	at com.example.encryption.DynamicStrategyAndSM2Test.testSM2Encryption(DynamicStrategyAndSM2Test.java:37) [test-classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_341]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_341]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_341]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_341]
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:686) [junit-platform-commons-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84) [junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$6(TestMethodTestDescriptor.java:212) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:208) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:137) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:71) ~[junit-jupiter-engine-5.6.3.jar:5.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at java.util.ArrayList.forEach(ArrayList.java:1259) ~[na:1.8.0_341]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51) ~[junit-platform-engine-1.6.3.jar:1.6.3]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128) ~[junit-platform-launcher-1.3.1.jar:1.3.1]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124) ~[surefire-junit-platform-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126) ~[surefire-booter-2.22.2.jar:2.22.2]
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418) ~[surefire-booter-2.22.2.jar:2.22.2]
Caused by: org.bouncycastle.util.encoders.DecoderException: exception decoding Hex string: String index out of range: 129
	at org.bouncycastle.util.encoders.Hex.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	at com.example.encryption.util.SM2Util.parsePublicKey(SM2Util.java:191) [classes/:na]
	... 68 common frames omitted
Caused by: java.lang.StringIndexOutOfBoundsException: String index out of range: 129
	at java.lang.String.charAt(String.java:658) ~[na:1.8.0_341]
	at org.bouncycastle.util.encoders.HexEncoder.decode(Unknown Source) ~[bcprov-jdk15on-1.70.jar:1.70.0]
	... 70 common frames omitted

2025-06-24 15:38:01.023  INFO 53772 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@5db6b9cd testClass = DynamicStrategyAndSM2Test, testInstance = com.example.encryption.DynamicStrategyAndSM2Test@4be1dce6, testMethod = testSM2Encryption@DynamicStrategyAndSM2Test, testException = java.lang.RuntimeException: SM2加密失败, mergedContextConfiguration = [WebMergedContextConfiguration@210ab13f testClass = DynamicStrategyAndSM2Test, locations = '{}', classes = '{class com.example.encryption.EncryptionDemoApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@55141def, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@235834f2, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@6b2ea799, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@22ff4249, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@604ed9f0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true]]
]]></system-out>
  </testcase>
  <testcase name="testAlgorithmTesting" classname="com.example.encryption.DynamicStrategyAndSM2Test" time="0.094"/>
  <testcase name="testStrategyStatistics" classname="com.example.encryption.DynamicStrategyAndSM2Test" time="0.07">
    <failure message="expected: &lt;true&gt; but was: &lt;false&gt;" type="org.opentest4j.AssertionFailedError"><![CDATA[org.opentest4j.AssertionFailedError: expected: <true> but was: <false>
	at com.example.encryption.DynamicStrategyAndSM2Test.testStrategyStatistics(DynamicStrategyAndSM2Test.java:218)
]]></failure>
    <system-out><![CDATA[2025-06-24 15:38:01.033  INFO 53772 --- [           main] o.s.t.c.transaction.TransactionContext   : Began transaction (1) for test context [DefaultTestContext@5db6b9cd testClass = DynamicStrategyAndSM2Test, testInstance = com.example.encryption.DynamicStrategyAndSM2Test@73d8c8d7, testMethod = testStrategyStatistics@DynamicStrategyAndSM2Test, testException = [null], mergedContextConfiguration = [WebMergedContextConfiguration@210ab13f testClass = DynamicStrategyAndSM2Test, locations = '{}', classes = '{class com.example.encryption.EncryptionDemoApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@55141def, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@235834f2, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@6b2ea799, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@22ff4249, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@604ed9f0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true]]; transaction manager [org.springframework.orm.jpa.JpaTransactionManager@20faaf77]; rollback [true]
=== 测试策略统计信息 ===
策略统计信息:
  globalOverrideEnabled: false
  totalStrategies: 5
  globalStrategy: PLAINTEXT_PRIORITY
  algorithmDistribution: {AES=5}
  enabledStrategies: 4
2025-06-24 15:38:01.043  INFO 53772 --- [           main] o.s.t.c.transaction.TransactionContext   : Rolled back transaction for test: [DefaultTestContext@5db6b9cd testClass = DynamicStrategyAndSM2Test, testInstance = com.example.encryption.DynamicStrategyAndSM2Test@73d8c8d7, testMethod = testStrategyStatistics@DynamicStrategyAndSM2Test, testException = org.opentest4j.AssertionFailedError: expected: <true> but was: <false>, mergedContextConfiguration = [WebMergedContextConfiguration@210ab13f testClass = DynamicStrategyAndSM2Test, locations = '{}', classes = '{class com.example.encryption.EncryptionDemoApplication}', contextInitializerClasses = '[]', activeProfiles = '{test}', propertySourceLocations = '{}', propertySourceProperties = '{org.springframework.boot.test.context.SpringBootTestContextBootstrapper=true}', contextCustomizers = set[org.springframework.boot.test.context.filter.ExcludeFilterContextCustomizer@55141def, org.springframework.boot.test.json.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer@235834f2, org.springframework.boot.test.mock.mockito.MockitoContextCustomizer@0, org.springframework.boot.test.web.client.TestRestTemplateContextCustomizer@6b2ea799, org.springframework.boot.test.autoconfigure.properties.PropertyMappingContextCustomizer@0, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverContextCustomizerFactory$Customizer@22ff4249, org.springframework.boot.test.context.SpringBootTestArgs@1, org.springframework.boot.test.context.SpringBootTestWebEnvironment@604ed9f0], resourceBasePath = 'src/main/webapp', contextLoader = 'org.springframework.boot.test.context.SpringBootContextLoader', parent = [null]], attributes = map['org.springframework.test.context.web.ServletTestExecutionListener.activateListener' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.populatedRequestContextHolder' -> true, 'org.springframework.test.context.web.ServletTestExecutionListener.resetRequestContextHolder' -> true]]
]]></system-out>
  </testcase>
</testsuite>