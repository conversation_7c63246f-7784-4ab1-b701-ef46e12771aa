# 前端集成说明

## 概述

前端管理界面已经成功集成到Spring Boot项目中，通过Spring Boot的静态资源服务提供Web界面，避免了跨域问题和404错误。

## 文件结构

```
src/main/resources/static/
├── index.html          # 主页面文件
└── app.js             # JavaScript应用逻辑

src/main/java/com/example/encryption/
├── controller/
│   ├── WebController.java              # Web页面控制器
│   └── ManagementApiController.java    # 管理API控制器
└── config/
    └── WebConfig.java                   # Web配置类
```

## 启动方式

### 1. 启动Spring Boot应用

```bash
# 方式一：使用Maven
mvn spring-boot:run

# 方式二：使用IDE
# 直接运行 EncryptionDemoApplication.main() 方法

# 方式三：打包后运行
mvn clean package
java -jar target/encryption-demo-0.0.1-SNAPSHOT.jar
```

### 2. 访问管理界面

启动成功后，在浏览器中访问：

- **主页面**：http://localhost:8080/
- **管理页面**：http://localhost:8080/admin
- **监控页面**：http://localhost:8080/monitor

所有路径都会显示同一个管理界面。

## 功能特性

### 🎯 系统状态概览
- 实时显示迁移状态、清理进度
- 加密字段数量统计
- 当前活跃任务数量
- 自动刷新（每5秒）

### 🔧 迁移策略管理
- 一键切换迁移策略：
  - 明文优先 (PLAINTEXT_PRIORITY)
  - 影子字段优先 (SHADOW_PRIORITY)
  - 仅影子字段 (SHADOW_ONLY)
- 紧急回滚功能

### 📊 数据迁移管理
- 启动迁移到指定版本
- 验证迁移结果
- 查看迁移统计信息
- 分表显示迁移进度

### 🧹 明文字段清理管理
- 分表清理任务管理
- 支持启动/暂停/恢复操作
- 实时清理进度监控
- 批量操作支持

### 📝 操作日志
- 记录所有操作和系统事件
- 按时间倒序显示
- 支持清空日志

## API接口

### 策略管理
- `GET /api/strategy/config` - 获取当前策略配置
- `POST /api/strategy/global` - 设置全局策略
- `POST /api/strategy/rollback/plaintext` - 紧急回滚

### 迁移管理
- `POST /api/migration/migrate/{version}` - 启动迁移
- `POST /api/migration/validate` - 验证迁移
- `GET /api/migration/stats` - 获取统计信息

### 清理管理
- `POST /api/migration/cleanup/start?tableName={table}` - 启动清理
- `POST /api/migration/cleanup/pause?tableName={table}` - 暂停清理
- `POST /api/migration/cleanup/resume?tableName={table}` - 恢复清理
- `GET /api/migration/cleanup/status` - 获取清理状态

## 技术实现

### 静态资源配置
通过 `WebConfig` 类配置静态资源处理：
- 静态文件路径：`classpath:/static/`
- 缓存时间：1小时
- 支持根路径访问

### API控制器
`ManagementApiController` 提供前端所需的所有API接口：
- 策略管理接口
- 迁移管理接口
- 清理管理接口
- 统计信息接口

### 前端技术栈
- **Vue.js 2.6.14** - 前端框架
- **Element UI 2.15.14** - UI组件库
- **Axios** - HTTP客户端
- **Font Awesome** - 图标库

## 使用说明

### 1. 系统监控
- 页面会自动每5秒刷新数据
- 可通过右上角开关控制自动刷新
- 手动刷新按钮可立即更新所有数据

### 2. 策略切换
```javascript
// 切换到影子字段优先模式
POST /api/strategy/global
{
  "strategy": "SHADOW_PRIORITY",
  "enableOverride": true
}
```

### 3. 启动迁移
```javascript
// 启动迁移到版本2
POST /api/migration/migrate/2
```

### 4. 清理管理
```javascript
// 启动患者表清理
POST /api/migration/cleanup/start?tableName=ych_patient

// 暂停清理
POST /api/migration/cleanup/pause?tableName=ych_patient

// 恢复清理
POST /api/migration/cleanup/resume?tableName=ych_patient
```

## 开发调试

### 1. 修改前端代码
直接修改 `src/main/resources/static/` 目录下的文件：
- `index.html` - 页面结构和样式
- `app.js` - JavaScript逻辑

### 2. 热重载
Spring Boot支持静态资源热重载，修改前端文件后：
- 无需重启应用
- 刷新浏览器即可看到更改

### 3. 调试API
可以通过浏览器开发者工具的Network面板查看API调用：
- 检查请求URL和参数
- 查看响应数据
- 排查错误信息

## 部署说明

### 1. 生产环境部署
```bash
# 打包应用
mvn clean package -Dmaven.test.skip=true

# 运行应用
java -jar target/encryption-demo-0.0.1-SNAPSHOT.jar

# 或者使用Docker
docker build -t data-encryption-app .
docker run -p 8080:8080 data-encryption-app
```

### 2. 配置文件
在 `application.yml` 中可以配置：
```yaml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 3600
```

### 3. 安全配置
生产环境建议添加：
- 访问认证
- HTTPS支持
- 请求限流
- 操作审计

## 故障排除

### 1. 页面无法访问
- 检查Spring Boot应用是否正常启动
- 确认端口8080是否被占用
- 查看控制台日志是否有错误

### 2. API调用失败
- 检查后端控制器是否正确实现
- 确认请求路径和参数是否正确
- 查看浏览器Network面板的错误信息

### 3. 静态资源404
- 确认文件在 `src/main/resources/static/` 目录下
- 检查 `WebConfig` 配置是否正确
- 重新编译并启动应用

### 4. 数据不更新
- 检查API接口是否返回正确数据
- 确认前端JavaScript是否有错误
- 查看浏览器控制台的错误信息

## 扩展功能

### 1. 添加新页面
在 `src/main/resources/static/` 目录下添加新的HTML文件，并在 `WebConfig` 中配置路由。

### 2. 添加新API
在 `ManagementApiController` 中添加新的接口方法，并在前端 `app.js` 中调用。

### 3. 集成认证
可以集成Spring Security来添加用户认证和权限控制。

### 4. 添加WebSocket
可以添加WebSocket支持来实现实时数据推送，提升用户体验。

## 总结

通过将前端文件集成到Spring Boot项目中，我们实现了：
- ✅ 避免跨域问题
- ✅ 统一部署和管理
- ✅ 简化开发和调试
- ✅ 提供完整的管理界面
- ✅ 支持实时监控和操作

现在您可以通过 `mvn spring-boot:run` 启动应用，然后访问 http://localhost:8080 来使用可视化管理界面了！
