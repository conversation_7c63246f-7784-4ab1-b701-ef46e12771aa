-------------------------------------------------------------------------------
Test set: com.example.encryption.migration.ShadowFieldMigrationTest
-------------------------------------------------------------------------------
Tests run: 9, Failures: 5, Errors: 0, Skipped: 0, Time elapsed: 0.94 s <<< FAILURE! - in com.example.encryption.migration.ShadowFieldMigrationTest
testShadowPriorityStrategy  Time elapsed: 0.113 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <110101199001011234> but was: <null>
	at com.example.encryption.migration.ShadowFieldMigrationTest.testShadowPriorityStrategy(ShadowFieldMigrationTest.java:98)

testMigrationStatistics  Time elapsed: 0.105 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <true> but was: <false>
	at com.example.encryption.migration.ShadowFieldMigrationTest.testMigrationStatistics(ShadowFieldMigrationTest.java:229)

testShadowOnlyStrategy  Time elapsed: 0.104 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <测试用户> but was: <null>
	at com.example.encryption.migration.ShadowFieldMigrationTest.testShadowOnlyStrategy(ShadowFieldMigrationTest.java:119)

testCreateMigrationUser  Time elapsed: 0.075 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <测试用户> but was: <null>
	at com.example.encryption.migration.ShadowFieldMigrationTest.testCreateMigrationUser(ShadowFieldMigrationTest.java:59)

testDirectEncryptStrategy  Time elapsed: 0.083 s  <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: not equal but was: <13900139000>
	at com.example.encryption.migration.ShadowFieldMigrationTest.testDirectEncryptStrategy(ShadowFieldMigrationTest.java:146)

