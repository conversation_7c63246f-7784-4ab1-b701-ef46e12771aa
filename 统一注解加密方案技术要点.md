# 统一注解加密方案技术要点

## 核心设计理念

### 统一注解驱动
所有三种方案都使用相同的@EncryptField注解标注需要加密的字段，通过ORM框架的自动转换机制处理加密，**无需手动调用加密算法类**。

### 自动化处理
- **JPA方式**：通过@Convert注解自动处理加密/解密
- **MyBatis方式**：通过TypeHandler自动处理加密/解密
- **开发简化**：开发者只需要标注字段，ORM框架自动处理加密逻辑

## 统一技术栈

### 1. 加密注解系统
```java
@EncryptField(
    description = "患者姓名",
    migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT, // 方案一、二使用
    strategyKey = "name"
)
@Convert(converter = EncryptConverter.class) // JPA方式
// 或者在MyBatis中使用 typeHandler=EncryptTypeHandler
private String name;
```

### 2. JPA自动转换器
```java
@Component
@Converter
public class EncryptConverter implements AttributeConverter<String, String> {
    @Autowired
    private EncryptionUtil encryptionUtil;
    
    @Override
    public String convertToDatabaseColumn(String attribute) {
        return encryptionUtil.encrypt(attribute); // 保存时自动加密
    }
    
    @Override
    public String convertToEntityAttribute(String dbData) {
        return encryptionUtil.decrypt(dbData); // 查询时自动解密
    }
}
```

### 3. MyBatis类型处理器
```java
@Component
@MappedTypes(String.class)
public class EncryptTypeHandler extends BaseTypeHandler<String> {
    @Autowired
    private EncryptionUtil encryptionUtil;
    
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, encryptionUtil.encrypt(parameter)); // 保存时自动加密
    }
    
    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String encrypted = rs.getString(columnName);
        return encryptionUtil.decrypt(encrypted); // 查询时自动解密
    }
}
```

## 三种方案的实现差异

### 方案一：备份数据库增量迁移

**实现方式**：
```java
// JPA方式：查询数据并重新保存，自动触发加密
Page<Patient> patients = backupPatientRepository.findAll(pageable);
backupPatientRepository.saveAll(patients); // @Convert自动加密

// MyBatis方式：查询明文数据，使用TypeHandler保存加密数据
List<Patient> patients = sqlSession.selectList("PatientMapper.selectPlaintext", params);
sqlSession.update("PatientMapper.updateWithEncryption", patient); // TypeHandler自动加密
```

**关键点**：
- 原表字段需要扩展长度以适应加密数据
- 使用@EncryptField注解标注字段
- 通过ORM框架自动处理加密转换

### 方案二：新表替换

**实现方式**：
```java
// JPA方式：从原表查询，保存到新表，自动加密
Patient patient = patientRepository.findById(id);
PatientNew newPatient = new PatientNew();
newPatient.setName(patient.getName()); // 直接赋值
patientNewRepository.save(newPatient); // @Convert自动加密

// MyBatis方式：查询原表明文，插入新表时自动加密
List<Patient> patients = sqlSession.selectList("PatientMapper.selectForMigration", params);
sqlSession.insert("PatientNewMapper.batchInsertWithEncryption", patients); // TypeHandler自动加密
```

**关键点**：
- 新表字段长度设计为加密后的长度
- 原表和新表都使用相同的@EncryptField注解
- 数据转换过程完全自动化

### 方案三：渐进式影子字段

**实现方式**：
```java
// 使用影子字段策略，支持多种迁移模式
@EncryptField(
    shadowField = "name_encrypted",
    migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY, // 可配置
    strategyKey = "name"
)
private String name;

@Column(name = "name_encrypted")
private String nameEncrypted; // 影子字段
```

**关键点**：
- 支持四种迁移策略的动态切换
- 配置驱动的策略管理
- 最完整的加密组件实现

## 开发流程简化

### 1. 实体类配置
```java
@Entity
public class Patient {
    // 只需要添加注解，无需其他代码
    @EncryptField(description = "患者姓名", strategyKey = "name")
    @Convert(converter = EncryptConverter.class) // JPA
    private String name;
    
    @EncryptField(description = "手机号", strategyKey = "phone")
    @Convert(converter = EncryptConverter.class) // JPA
    private String phone;
}
```

### 2. MyBatis配置
```xml
<!-- 只需要在需要加密的字段上指定TypeHandler -->
<insert id="insertPatient">
    INSERT INTO ych_patient (name, phone) VALUES 
    (#{name,typeHandler=EncryptTypeHandler}, #{phone,typeHandler=EncryptTypeHandler})
</insert>
```

### 3. 业务代码
```java
@Service
public class PatientService {
    // 业务代码无需关心加密逻辑
    public void savePatient(Patient patient) {
        patientRepository.save(patient); // 自动加密
    }
    
    public Patient getPatient(Long id) {
        return patientRepository.findById(id); // 自动解密
    }
}
```

## 技术优势

### 1. 开发简化
- **无需手动加密**：开发者不需要调用加密算法类
- **注解驱动**：只需要标注字段，框架自动处理
- **透明化处理**：业务代码无需关心加密逻辑

### 2. 维护便利
- **统一管理**：所有加密逻辑集中在注解和转换器中
- **易于调试**：加密逻辑清晰，便于排查问题
- **配置灵活**：支持不同字段使用不同策略

### 3. 扩展性强
- **ORM无关**：同时支持JPA和MyBatis
- **策略可扩展**：可以轻松添加新的迁移策略
- **向后兼容**：不影响现有业务代码

## 实施要点

### 1. 数据库准备
```sql
-- 扩展字段长度以适应加密数据
ALTER TABLE ych_patient 
MODIFY COLUMN name VARCHAR(500),
MODIFY COLUMN phone VARCHAR(500),
MODIFY COLUMN id_card_no VARCHAR(500);
```

### 2. 注解配置
```java
// 方案一、二：使用直接加密模式
@EncryptField(
    migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
    strategyKey = "name"
)

// 方案三：使用影子字段模式
@EncryptField(
    shadowField = "name_encrypted",
    migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY,
    strategyKey = "name"
)
```

### 3. 迁移执行
```java
// 方案一：查询并重新保存
List<Patient> patients = repository.findAll();
repository.saveAll(patients); // 自动加密

// 方案二：从原表复制到新表
Patient patient = originalRepository.findById(id);
PatientNew newPatient = convertToNew(patient);
newRepository.save(newPatient); // 自动加密

// 方案三：配置驱动的渐进式迁移
migrationService.migrateToVersion(2); // 自动处理
```

## 总结

通过统一的注解驱动加密方案，三种迁移方案都能够：
- **简化开发**：无需手动调用加密算法
- **自动处理**：ORM框架自动处理加密/解密
- **统一管理**：使用相同的注解和工具类
- **灵活配置**：支持不同的迁移策略

这种设计大大降低了开发复杂度，提高了代码的可维护性和可扩展性。
