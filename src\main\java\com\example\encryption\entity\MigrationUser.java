package com.example.encryption.entity;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.converter.EncryptConverter;
import com.example.encryption.converter.ShadowFieldEncryptConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 影子字段迁移演示用户实体类
 * 展示不同迁移策略的使用方式
 */
@Entity
@Table(name = "migration_users")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MigrationUser {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户名 - 不加密
     */
    @Column(name = "username", nullable = false, unique = true, length = 50)
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 手机号 - 传统直接加密方式
     * 继续使用现有的加密方式，无需迁移
     */
    @EncryptField(
        description = "用户手机号",
        migrationStrategy = EncryptField.MigrationStrategy.DIRECT_ENCRYPT,
        strategyKey = "phone"
    )
    @Convert(converter = EncryptConverter.class)
    @Column(name = "phone", length = 500)
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 邮箱 - 明文字段优先策略
     * 适用于迁移初期，保证数据安全的同时支持快速回滚
     * 读取时优先从明文字段读取，写入时同时写入明文和影子字段
     */
    @EncryptField(
        description = "用户邮箱",
        shadowField = "email_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.PLAINTEXT_PRIORITY,
        strategyKey = "email",
        version = 2
    )
    @Column(name = "email", length = 255)
    @Email(message = "邮箱格式不正确")
    private String email;

    /**
     * 邮箱影子字段 - 存储加密后的邮箱
     * 这个字段在数据库中存在，但在业务逻辑中不直接使用
     */
    @Column(name = "email_encrypted", length = 500)
    private String emailEncrypted;

    /**
     * 身份证号 - 影子字段优先策略
     * 适用于迁移中期，逐步切换到加密存储
     * 读取时优先从影子字段读取，写入时只写入影子字段
     */
    @EncryptField(
        description = "身份证号",
        shadowField = "id_card_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_PRIORITY,
        strategyKey = "idCard",
        version = 2
    )
    @Column(name = "id_card", length = 255)
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$",
             message = "身份证号格式不正确")
    private String idCard;

    /**
     * 身份证号影子字段 - 存储加密后的身份证号
     */
    @Column(name = "id_card_encrypted", length = 500)
    private String idCardEncrypted;

    /**
     * 真实姓名 - 仅影子字段策略
     * 适用于迁移完成后，完全使用加密存储
     * 只使用影子字段进行读写，完全忽略明文字段
     */
    @EncryptField(
        description = "真实姓名",
        shadowField = "real_name_encrypted",
        migrationStrategy = EncryptField.MigrationStrategy.SHADOW_ONLY,
        strategyKey = "realName",
        version = 3
    )
    @Column(name = "real_name", length = 255)
    private String realName;

    /**
     * 真实姓名影子字段 - 存储加密后的真实姓名
     */
    @Column(name = "real_name_encrypted", length = 500)
    private String realNameEncrypted;

    /**
     * 数据版本号 - 用于标识数据的加密版本
     * 便于后续升级和回滚操作
     */
    @Column(name = "data_version", nullable = false)
    private Integer dataVersion = 1;

    /**
     * 迁移状态 - 标识当前记录的迁移状态
     */
    @Column(name = "migration_status", nullable = false)
    @Enumerated(EnumType.STRING)
    private MigrationStatus migrationStatus = MigrationStatus.NOT_MIGRATED;

    /**
     * 年龄 - 不敏感，不加密
     */
    @Column(name = "age")
    private Integer age;

    /**
     * 用户状态 - 不敏感，不加密
     */
    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private UserStatus status = UserStatus.ACTIVE;

    /**
     * 创建时间 - 不敏感，不加密
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    /**
     * 更新时间 - 不敏感，不加密
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 迁移状态枚举
     */
    public enum MigrationStatus {
        NOT_MIGRATED("未迁移"),
        MIGRATING("迁移中"),
        MIGRATED("已迁移"),
        ROLLBACK("已回滚");

        private final String description;

        MigrationStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        SUSPENDED("暂停"),
        DELETED("已删除");

        private final String description;

        UserStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * JPA生命周期回调 - 创建前
     */
    @PrePersist
    protected void onCreate() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        if (this.dataVersion == null) {
            this.dataVersion = 1;
        }
        processFieldsForSave();
    }

    /**
     * JPA生命周期回调 - 更新前
     */
    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = LocalDateTime.now();
        processFieldsForSave();
    }

    /**
     * JPA生命周期回调 - 加载后
     */
    @PostLoad
    protected void onLoad() {
        processFieldsForLoad();
    }

    /**
     * 处理字段数据（保存时）
     */
    private void processFieldsForSave() {
        // 处理邮箱字段（明文优先策略）
        if (org.springframework.util.StringUtils.hasText(this.email)) {
            // 同时保存明文和密文
            this.emailEncrypted = com.example.encryption.util.AESGCMUtil.encrypt(this.email);
        }

        // 处理身份证号字段（影子字段优先策略）
        if (org.springframework.util.StringUtils.hasText(this.idCard)) {
            // 只保存密文，清空明文
            this.idCardEncrypted = com.example.encryption.util.AESGCMUtil.encrypt(this.idCard);
            this.idCard = null;
        }

        // 处理真实姓名字段（仅影子字段策略）
        if (org.springframework.util.StringUtils.hasText(this.realName)) {
            // 只保存密文
            this.realNameEncrypted = com.example.encryption.util.AESGCMUtil.encrypt(this.realName);
            this.realName = null;
        }
    }

    /**
     * 处理字段数据（加载时）
     */
    private void processFieldsForLoad() {
        // 处理邮箱字段（明文优先策略）
        if (!org.springframework.util.StringUtils.hasText(this.email) && org.springframework.util.StringUtils.hasText(this.emailEncrypted)) {
            this.email = com.example.encryption.util.AESGCMUtil.decrypt(this.emailEncrypted);
        }

        // 处理身份证号字段（影子字段优先策略）
        if (org.springframework.util.StringUtils.hasText(this.idCardEncrypted)) {
            this.idCard = com.example.encryption.util.AESGCMUtil.decrypt(this.idCardEncrypted);
        } else if (org.springframework.util.StringUtils.hasText(this.idCard)) {
            // 历史明文数据，保持不变
        }

        // 处理真实姓名字段（仅影子字段策略）
        if (org.springframework.util.StringUtils.hasText(this.realNameEncrypted)) {
            this.realName = com.example.encryption.util.AESGCMUtil.decrypt(this.realNameEncrypted);
        }
    }

    /**
     * 获取当前实体的迁移策略摘要
     * 用于调试和监控
     */
    public String getMigrationSummary() {
        return String.format(
            "MigrationUser[id=%d, version=%d, status=%s, strategies={phone:DIRECT, email:PLAINTEXT_PRIORITY, idCard:SHADOW_PRIORITY, realName:SHADOW_ONLY}]",
            id, dataVersion, migrationStatus
        );
    }
}
