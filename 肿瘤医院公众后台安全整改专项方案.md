# 肿瘤医院公众后台安全整改专项方案

## 一、系统基本情况

### 系统描述与功能

系统是为云南省肿瘤医院公众号患者服务开发的功能，主要包含：预约挂号、门诊医保缴费、门诊现金缴费、诊间挂号缴费、检验检查报告查询、体检报告、分子诊断中心报告、护理专科门诊、取号、排队候诊、药品目录查询、非药品目录查询、用药指导、院内导航、住院缴费、出院结算、病案复印、出院带药、住院陪护、费用补缴功能。

系统包含：

*   **公众号患者端入口**
    *   URL: <https://ych-witmed.ynhdkc.com/>
    *   GitLab: \[未提供]
*   **公众号后台管理系统**
    *   URL: <https://ych-admin.ynhdkc.com/user/login>
    *   GitLab: <https://gitlab.ynhdkc.com/ynszlyy/backend/ych-witmed-server>

### 当前版本与部署架构
| 类型     | 版本      |
| -------- | -------- |
| jdk   | 11 |
| mysql   |5.7   |
| redis | 5.0.9  |
| springboot     | 2.7.18                         |

### 业务重要性评估

此系统对肿瘤医院自身业务开展和患者就诊有着重要作用，同时包含大量用户隐私数据，包括患者姓名、身份证号（图片）、手机号、银行卡、家庭住址、就诊信息等。

| 数据类型     | 数据量      | 主要敏感信息                                 |
| -------- | -------- | -------------------------------------- |
| 公民个人信息   | 657,029条 | 姓名、身份证号、手机号、家庭住址                       |
| 出院结算数据   | 1,112条   | 患者信息、医疗费用、支付信息                         |
| 病案复印申请信息 | 21,502条  | 姓名、身份证号、手机号、家庭住址、身份证正反面照片、手持身份证照片、银行卡号 |
| 预约记录     | 870,594条 | 患者信息、预约科室、疾病信息                         |
| 补缴记录     | 422条     | 患者信息、财务数据、支付信息                         |
|**数据库总数据大小**| 7541.46M||

其中"病案复印"除包含用户基础信息外还有用户身份证正反面照片、手持身份证照片等信息，此外包含退款等敏感操作。若发生数据安全事件将造成恶劣社会影响和经济损失。

### 数据分类分级评估

根据《数据安全法》要求进行数据分类分级评估：

| 数据类别   | 具体数据类型           | 敏感程度 | 保护要求                |
| ------ | ---------------- | ---- | ------------------- |
| 重要数据   | 挂号数据患者选择挂号的科室、医生      | 高    | 访问控制、审计追踪 |
| 个人敏感信息 | 身份证号码及图片、银行卡信息   | 高    | 加密存储、最小化展示、严格访问控制   |
| 个人敏感信息 | 家庭住址、详细就诊记录      | 中高   | 部分脱敏、访问控制、授权查看      |
| 一般个人信息 | 姓名、基本联系方式        | 中    | 部分脱敏、常规保护           |

**本系统数据安全保护级别**：**二级（重要）**

## 二、安全风险详细分析

### 已发现漏洞清单与风险等级

| 序号 | 风险点                     | 风险等级 | 主要影响                                      |
| -- | ----------------------- | ---- | ----------------------------------------- |
| 1  | 未做密码过期、密码强度、登录重试限制      | 高    | 密码容易被暴力破解；长期使用同一密码增加泄露风险；缺乏登录限制可能导致系统遭受攻击 |
| 2  | 病案复印未做敏感数据脱敏            | 高    | 敏感数据可能被未授权人员获取，引发数据泄露、法律风险、经济损失           |
| 3  | 诊疗服务管理详情包含敏感数据，管理员可随意查看挂号数据 | 高    | 敏感医疗数据可能被滥用，造成患者隐私泄露                      |
| 4  | 出院结算详情包含敏感数据（姓名、银行卡号、开户行、联系电话、住址），管理员可随意查看   | 高    | 财务敏感数据未保护可能导致隐私泄露和财务安全风险                  |
| 5  | 预约查询详情包含敏感数据，管理员可随意查看   | 高    | 可能导致患者就诊隐私信息泄露                            |
| 6  | 补缴包含敏感数据（个人数据），管理员可随意查看       | 高    | 可能导致患者财务和就诊隐私信息泄露                         |
| 7  | 缺少操作审计日志                | 高    | 安全事件发生时难以追溯原因、影响范围及责任人，延误处理时机             |
| 8  | 账号未做单点登录限制，同一账号可同时多端登录  | 高    | 高权限账号被多端同时使用增加被滥用可能性；难以追踪具体操作来源           |
| 9  | 访问缺少多因子认证               | 高    | 仅依赖单一因素认证，密码一旦泄露，攻击者可轻松进入系统并获取敏感数据        |
| 10  | 重要的敏感数据未做加密存储              | 高    | 如果数据库系统被攻破或其他原因导致数据泄露，敏感数据明文存储可能会造成信息被乱用，造成恶劣影响        |

### 脆弱性根因分析

| 风险类别          | 具体风险点                   | 根本原因                         |
| ------------- | ----------------------- | ---------------------------- |
| **身份验证措施不完全** | 未做密码过期、密码强度、登录重试限制      | 系统设计阶段未将安全需求纳入核心设计           |
|               | 账号未做单点登录限制，同一账号可同时多端登录  | 会话管理机制设计不完善                  |
|               | 访问缺少多因子认证               | 认证机制过于简单，未考虑高安全场景需求          |
| **未做数据保密性措施** | 病案复印未做敏感数据脱敏            | 缺乏数据分类分级管理和最小必要原则实施          |
|               | 诊疗服务管理详情包含敏感数据，管理员可随意查看 | 未建立数据访问控制和授权机制               |
|               | 出院结算详情包含敏感数据，管理员可随意查看   | 缺乏角色权限管理和最小授权原则              |
|               | 预约查询详情包含敏感数据，管理员可随意查看   | 未实施数据脱敏和访问控制策略               |
|               | 补缴包含敏感数据，管理员可随意查看       | 财务数据保护机制缺失                   |
|               | 敏感信息数据库储存未加密       | 未实施公民敏感数据加密存储措施                   |
| **安全审计功能缺失**  | 缺少操作审计日志                | 系统设计未考虑完整安全管理要求，特别是审计与责任追溯机制 |

### 潜在安全威胁评估

| 法律条款 | 威胁类别      | 威胁级别 | 影响范围         | 潜在后果                                                               |
| --------- | ---- | ------------ |--------| ------------------------------------------------------------------ |
|GB/T-2022239 <br>8.1.4.1身份鉴别 |身份验证措施不完全 | 高    | 全系统       | 1. 系统账号易被猜解或暴力破解<br>2. 无多因子认证，口令泄露造成未授权登录<br>3. 系统管理权限被滥用          |
| GB/T-2022239<br> 8.1.4.7 数据完整性规定<br>8.1.4.8数据保密性规|未做数据保密性措施 | 高    | 657,029名患者信息 | 1. 造成无法挽回的公民个人信息泄露<br>2. 违反《数据安全法》和《个人信息保护法》<br>3. 医院面临重大声誉损失和法律责任 |
| GB/T-2022239 <br>8.1.4.3 安全审计|安全审计功能缺失  | 高    | 全系统管理操作      | 1. 无法确定问题发生时间和责任人<br>2. 无法实现责任认定和事件追溯<br>3. 违规操作难以发现和制止            |

**总结**：该系统存在较大的信息安全风险，特别是在个人敏感信息保护方面的缺陷，不符合《数据安全法》对医疗数据作为重要数据的保护要求，需要全面整改。

## 三、整改技术方案

### 具体修复措施（针对每个漏洞）

#### 1. 密码安全策略实施

| 技术层面     | 具体措施   | 实现细节                                               |
| -------- | ------ | ------  |
| **前端处理** | 密码强度校验 | - 长度≥10位、≤20位<br>- 包含大小写字母、数字和特殊字符<br>- 实时密码强度提示
|          | 密码过期处理 | - 登录时调用后端接口校验密码过期状态<br>- 过期/临期密码自动跳转修改页面（上一次修改超过90天，强制进入修改流程，7天内将过期，提示建议修改）
|          | 短信验证机制 | - 增加短信验证码获取和提交逻辑<br>- 验证码倒计时显示                                    |
|          | 审计功能界面 | - 后台管理系统增加操作审计页面<br>- 提供多条件查询功能                                |
|          | 敏感数据查询操作二次校验 | - 增加二次校验获取验证码和校验验证码入口按钮 |0.8
| **后端处理** | 密码策略实施 | - 记录密码修改记录<br>- 密码90天过期机制<br>- 10-20位密码强度校验<br>- 历史密码查重（不能与前3次相同）
|          | 登录安全限制 | - 连续5次登录失败锁定账户1小时<br>- 短信验证码频率限制（同一手机号一分钟限发一条）<br> - 登录短信验证码校验<br> - 互踢下线<br>- token60分钟注销  |2.5|
|          | 审计日志实现 | - 通过AOP记录操作日志<br>- 记录操作人、操作时间、操作接口、请求数据            |
|          | 数据导出控制 | - 导出操作二次校验确认<br>- 单次导出限制1000条<br>- 导出操作特别记录                       |
|          | 敏感操作二次校验 | - 需要通过短信验证码对涉及的敏感操作进行二次校验，单次授权30分钟，可通过此校验查看关键敏感数<br>- 增加脱敏白名单，只对白名单内用户开放敏感操作权限，同时需要二次校验                       |1.5|
|          | 敏感数据脱敏 | - 通过现有工具对涉及敏感数据脱敏<br>- 二次校验后允许授权用户获取未脱敏数据，满足业务需求            |
||敏感数据加密存储|- 对敏感数据字段进行标注，通过AES-256-GCM加密算法组合，在数据存取之前做加解密<br>- 要编写脚本来对现有数据库敏感数据进行加密<br>- 搭建相关加解密测试库和环境，验证加解密效率，评估服务停机时长|3.5          |

#### 2. 数据脱敏与访问控制实施

| 安全功能     | 涉及模块   | 实现方式                                                                           |
| -------- | ------ | ------------------------------------------------------------------------------ |
| **数据脱敏** | 病案复印   | - 姓名显示首字符，其余用*替代（张\*\*）<br>- 身份证号显示前6位和后4位（123456*\*\*\*1234）<br>- 身份证图片控制访问和二次校验 |
|          | 诊疗服务管理 | - 手机号显示前3位和后4位（139\*\*\*\*1234） <br> - 姓名显示首字符，其余用替代（张\*\*）<br> - 身份证号显示前6位和后4位（123456***1234）|
|          | 出院结算   | - 银行卡号仅显示后4位（\*\*\*\*\*\*\*\*\*\*\*\*1234）<br>- 开户行只显示前两位和后两位（云南\*\*支行）     <br>   - 邮寄地址只显示前三位（云南省**）<br>- 手机号显示前3位和后4位（139\*\*\*\*1234）    |
|          | 预约查询   |- 身份证号显示前6位和后4位（123456***1234）<br>- 手机号显示前3位和后4位（139\*\*\*\*1234）                                     |
|          | 补缴信息   | - 支付方式信息部分隐藏<br>- 财务数据脱敏显示                                                     |
| **访问控制** | 所有敏感模块 | - 短信二次验证机制<br>- 授权令牌颁发（有效期30分钟）<br>- 白名单用户管理<br>- 权限细分控制  <br>- 如果如要查看未脱敏数据需要二次授权并记录操作日志                      |

#### 3. 单点登录与多因子认证

| 功能        | 技术实现                                                    | 预期效果                                    |
| --------- | ------------------------------------------------------- | --------------------------------------- |
| **单点登录**  | - 新token颁发时注销旧token<br>- Redis存储会话状态<br>- JWT+状态结合的认证机制 | - 同一账号互踢下线<br>- 准确追踪用户会话<br>- 防止会话劫持    |
| **多因子认证** | - 账号密码+短信验证码<br>- 敏感操作二次验证<br>    | - 降低账号被盗风险<br>- 提高敏感操作安全性 |

#### 4. 数据库敏感数据加密存储

| 功能        | 技术实现                                                    | 预期效果                                    |
| --------- | ------------------------------------------------------- | --------------------------------------- |
| **敏感数据加密存储**  | - 通过jpa的Attribute Converter 机制实现字段的透明加密 / 解密<br>- 自定义加密注解，用于标注实体类需要加解密的字段<br>- 实现加密工具类，能支持AES-256-GCM算法 | - 敏感数据在数据库中是加密后的密文，新增和更新时自动加密<br>- 应用从数据库查询时，自动根据算法解密    |

### 5. 技术路径选择与理由

| 技术路径                | 技术实现                                                                                                                                                                                                                                                          | 选择理由                             |
| ------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------- |
| **数据脱敏**            | - 使用现有工程的相关工具类和实现<br>- 脱敏注解：src/main/java/com/ynszlyy/common/infrastructure/desensitization/SensitiveProperty.java<br>- 使用示例：src/main/java/com/ynszlyy/witmed/entity/Patient.java<br>- 脱敏策略实现：src/main/java/com/ynszlyy/common/infrastructure/desensitization | 减少整改工作量和风险，现有脱敏策略已能满足需求          |
| **多因子认证**           | - 项目已集成腾讯云短信<br>- 扩展现有短信验证功能到登录和敏感操作                                                                                                                                                                                                                          | 充分利用现有技术资源，降低开发成本                |
| **基于拦截器的审计实现**      | - 使用拦截器实现统一的审计日志记录<br>- 记录关键操作和数据访问<br>   |                             |
| **操作审计记录**          | - 通过API接入阿里云SLS日志服务<br>- 集中化日志存储和管理                                                                                                                                                                                                                           | 不会对现有技术体系造成影响，降低风险，减少数据库数据量;方便操作日志滚动删除管理和全文索引和防日志数据篡改|                                                                                                                                                                          | 集中式审计实现，避免在每个业务代码中添加审计逻辑，减少代码侵入性 |
| **JWT结合Redis的会话管理** | - 使用JWT进行身份验证<br>- Redis管理token状态和单点登录控制                                                                                                                                                                                                                      | 既保持无状态API的灵活性，又能实现会话管理和单点登录控制    |
| **数据库加解密实现** | - JPA 的 Attribute Converter 机制实现字段的透明加密 / 解密     <br> - 实现AttributeConverter 接口，处理实体字段与数据库字段间的转换，在转换器完成对标注字段的加解密    | - 项目已集成jpa，减少对项目技术选型改动，降低改动风险 <br> -不需要修改现有数据模型，也不会影响业
| **加密算法** | - AES-256-GCM    |- 目前最安全且容易使用的加密算法组合<br>  - 提供加密和认证，防止数据被篡改<br>- 高性能，尤其适合大量数据处理 <br>- 避免了填充问题，简化了实现    |
### 5. 数据加解密方案
#### 5.1 需要加密字段和表梳理
#### 一、核心敏感数据表（优先级：高）

| 表名 | 字段名 | 字段类型 | 字段说明 | 数据量 | 加密类型 |
|------|--------|----------|----------|---------|----------|
| **ych_patient** | id_card_no | varchar(18) | 就诊人身份证 | 776,493条 | 身份证 |
| | name | varchar(60) | 就诊人姓名 | | 姓名 |
| | phone | varchar(11) | 就诊人手机号 | | 手机号 |
| | address | varchar(255) | 就诊人地址 | | 地址 |
| **ych_appoint_record** | patient_id_card | varchar(50) | 就诊人身份证号 | 927,385条 | 身份证 |
| | patient_name | varchar(60) | 就诊人姓名 | | 姓名 |
| | patient_phone | varchar(20) | 就诊人手机号 | | 手机号 |
| **ych_mc_apply** | patient_name | varchar(20) | 患者姓名 | 21,557条 | 姓名 |
| | patient_id_card | varchar(20) | 患者身份证号 | | 身份证 |
| | apply_name | varchar(20) | 申请人姓名 | | 姓名 |
| | apply_idcard | varchar(20) | 申请人身份证 | | 身份证 |
| | apply_tel | varchar(20) | 申请人手机号 | | 手机号 |
| | receive_name | varchar(20) | 收件人 | | 姓名 |
| | receive_tel | varchar(20) | 收件人电话 | | 手机号 |
| | receive_address | varchar(200) | 收件地址 | | 地址 |

#### 二、财务敏感数据表（优先级：高）

| 表名 | 字段名 | 字段类型 | 字段说明 | 数据量 | 加密类型 |
|------|--------|----------|----------|---------|----------|
| **ych_inpatient_settle_apply** | patient_name | varchar(32) | 姓名 | 1,125条 | 姓名 |
| | account_name | varchar(32) | 户名 | | 姓名 |
| | account_bank | varchar(255) | 开户银行 | | 银行信息 |
| | account_sub_bank | varchar(255) | 开户支行 | | 银行信息 |
| | account_no | varchar(64) | 银行账号 | | 银行卡号 |
| | express_name | varchar(32) | 收件人姓名 | | 姓名 |
| | express_phone | varchar(32) | 收件人手机号 | | 手机号 |
| | express_address | varchar(255) | 收件地址 | | 地址 |
| | email | varchar(255) | 邮件地址 | | 邮箱 |

#### 三、医保相关数据表（优先级：中）

| 表名 | 字段名 | 字段类型 | 字段说明 | 数据量 | 加密类型 |
|------|--------|----------|----------|---------|----------|
| **ych_mi_his_pay_order** | user_card_no | varchar(32) | 证件号码 | 56,240条 | 身份证 |
| | user_name | varchar(32) | 真实姓名 | | 姓名 |
| **ych_mi_wx_pay_order** | user_card_no | varchar(32) | 证件号码 | 48,937条 | 身份证 |
| | user_name | varchar(32) | 真实姓名 | | 姓名 |

#### 四、用户基础信息表（优先级：中）

| 表名 | 字段名 | 字段类型 | 字段说明 | 数据量 | 加密类型 |
|------|--------|----------|----------|---------|----------|
| **ych_user** | mobile | varchar(20) | 电话号 | 977,914条 | 手机号 |
| | name | varchar(25) | 姓名 | | 姓名 |
| | idcard | varchar(20) | 身份证号 | | 身份证 |
| | address | varchar(255) | 住址 | | 地址 |
| **ych_escort** | name | varchar(60) | 陪护人姓名 | 1,537条 | 姓名 |
| | id_card_no | varchar(18) | 陪护人身份证 | | 身份证 |
| | phone | varchar(11) | 陪护人手机号 | | 手机号 |
| | address | varchar(255) | 陪护人地址 | | 地址 |

#### 五、门诊相关数据表（优先级：低）

| 表名 | 字段名 | 字段类型 | 字段说明 | 数据量 | 加密类型 |
|------|--------|----------|----------|---------|----------|
| **ych_mz_pay_order** | patient_name | varchar(60) | 就诊人姓名 | 3,593,637条 | 姓名 |
| **ych_mz_scan_pay_order** | patient_name | varchar(60) | 就诊人姓名 | 465,762条 | 姓名 |
| | phone | varchar(60) | 就诊人手机 | | 手机号 |
| **ych_medical_record_copy** | patient_name | varchar(30) | 患者姓名 | 186条 | 姓名 |
| | express_name | varchar(32) | 收件人姓名 | | 姓名 |
| | express_phone | varchar(32) | 收件人手机号 | | 手机号 |
| | express_address | varchar(255) | 收件地址 | | 地址 |

#### 六、其他数据表（优先级：低）

| 表名 | 字段名 | 字段类型 | 字段说明 | 数据量 | 加密类型 |
|------|--------|----------|----------|---------|----------|
| **ych_black_user** | mobile | varchar(20) | 手机号 | 少量 | 手机号 |
| **ych_block_patient** | patient_name | varchar(30) | 就诊人姓名 | 22条 | 姓名 |
| **ych_complaint** | tel | varchar(16) | 联系电话 | 5条 | 手机号 |

#### 统计汇总

##### 按表统计
- **总计需要处理的表数**：16个
- **核心表**（数据量大于10万）：
  - ych_patient（77.6万条）
  - ych_appoint_record（92.7万条）
  - ych_user（97.8万条）
  - ych_mz_pay_order（359万条）
  - ych_mz_scan_pay_order（46.6万条）

##### 按字段类型统计
| 字段类型 | 涉及字段数 | 主要分布 |
|----------|------------|----------|
| 身份证号 | 9个 | patient、user、mc_apply等表 |
| 姓名 | 17个 | 几乎所有涉及个人信息的表 |
| 手机号 | 11个 | patient、user、escort等表 |
| 地址 | 7个 | patient、user、mc_apply等表 |
| 银行账号 | 3个 | inpatient_settle_apply表 |
| 邮箱 | 1个 | inpatient_settle_apply表 |

##### 数据量估算
- **总数据量**：约750万条记录
- **需要加密的字段实例数**：约2000万个（考虑每条记录多个字段）
- **预计加密时间**：
  - 单线程：约2小时（按1000条/秒计算）
  - 8线程并行：约1小时

#### 5.2 加密方案技术选型

基于阿里云RDS MySQL数据库环境，我们提供三种数据加密迁移技术方案：
- **完整的实现poc**：https://gitlab.ynhdkc.com/dyt/backend/dyt-data-encryption-decryption

**统一技术栈**：
- 所有方案都使用统一的@EncryptField注解标注需要加密的字段
- 通过ORM框架（JPA/MyBatis）的自动转换机制处理加密，无需手动调用加密算法
- 避免使用存储过程，全部使用Java应用层处理
- 支持共享RDS环境，控制并发度，避免长事务

##### 方案一：备份数据库增量迁移方案

**技术原理**：
通过数据库备份恢复机制，先处理存量数据，再通过停服处理增量数据，最后切换数据源。
**使用统一的@EncryptField注解，通过ORM框架自动处理加密。**

**实施步骤**：

1. **准备阶段**
   ```bash
   # 1. 创建备份数据库实例
   # 通过阿里云RDS控制台创建新实例
   # 配置：与生产环境相同规格

   # 2. 全量备份生产数据库
   mysqldump -h prod-rds-host -u username -p \
     --single-transaction \
     --routines \
     --triggers \
     --all-databases > full_backup_$(date +%Y%m%d_%H%M%S).sql

   # 3. 恢复到备份数据库
   mysql -h backup-rds-host -u username -p < full_backup_*.sql
   ```

2. **存量数据加密处理（使用注解自动加密）**
   ```java
   @Service
   public class BackupDataEncryptionService {

       @Autowired
       @Qualifier("backupPatientRepository")
       private PatientRepository backupPatientRepository;

       /**
        * 使用JPA自动加密机制处理存量数据
        * 只需要查询数据并重新保存，@Convert注解会自动处理加密
        */
       @Transactional("backupTransactionManager")
       public void encryptPatientData() {
           int batchSize = 100; // 小批次，避免长事务
           int pageNumber = 0;

           while (true) {
               // 分页查询原始数据
               Pageable pageable = PageRequest.of(pageNumber, batchSize, Sort.by("id"));
               Page<Patient> patientPage = backupPatientRepository.findAll(pageable);

               if (patientPage.isEmpty()) {
                   break;
               }

               // 重新保存数据，@Convert注解会自动触发加密
               List<Patient> patients = patientPage.getContent();
               backupPatientRepository.saveAll(patients);

               pageNumber++;

               // 控制处理速度，避免对共享RDS造成压力
               try {
                   Thread.sleep(100);
               } catch (InterruptedException e) {
                   Thread.currentThread().interrupt();
                   break;
               }
           }
       }
   }

   // 实体类配置（使用直接加密模式）
   @Entity
   @Table(name = "ych_patient")
   public class Patient {
       @EncryptField(
           description = "患者姓名",
           migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
           strategyKey = "name"
       )
       @Convert(converter = EncryptConverter.class)
       @Column(name = "name", length = 500) // 扩展长度适应加密数据
       private String name;

       // 其他字段...
   }
   ```

3. **增量数据处理**
   ```bash
   # 1. 记录开始时间点
   START_TIME=$(date '+%Y-%m-%d %H:%M:%S')

   # 2. 停止应用服务
   systemctl stop application-service

   # 3. 导出增量数据
   mysqldump -h prod-rds-host -u username -p \
     --single-transaction \
     --where="updated_at >= '$START_TIME'" \
     --tables ych_patient ych_user ych_appoint_record > incremental_data.sql

   # 4. 处理增量数据（使用JPA自动加密）
   # 通过Java应用处理增量数据
   curl -X POST http://localhost:8080/api/migration/incremental \
     -H "Content-Type: application/json" \
     -d '{"startTime":"'$START_TIME'","targetDatabase":"backup"}'
   ```

4. **数据源切换**
   ```bash
   # 1. 修改应用配置
   # 将数据库连接指向备份数据库
   sed -i 's/prod-rds-host/backup-rds-host/g' application.yml

   # 2. 启动应用服务
   systemctl start application-service

   # 3. 验证服务正常
   curl -f http://localhost:8080/health || exit 1
   ```

**优势**：
- 存量数据处理时间充足，可以精细化处理
- 增量数据量小，停服时间短
- 数据一致性有保障

**劣势**：
- 需要额外的数据库实例成本
- 切换过程需要停服
- 回滚相对复杂

**预计停服时间**：2-4小时（取决于增量数据量）

##### 方案二：新表替换方案

**技术原理**：
创建新的表结构，将加密后的数据插入新表，通过表重命名实现快速切换。
**使用统一的@EncryptField注解，通过ORM框架自动处理加密。**

**实施步骤**：

1. **创建新表结构**
   ```sql
   -- 为每个需要加密的表创建新表
   CREATE TABLE ych_patient_new LIKE ych_patient;
   CREATE TABLE ych_user_new LIKE ych_user;
   CREATE TABLE ych_appoint_record_new LIKE ych_appoint_record;
   -- ... 其他表

   -- 修改字段长度以适应加密后的数据
   ALTER TABLE ych_patient_new
   MODIFY COLUMN name VARCHAR(500),
   MODIFY COLUMN phone VARCHAR(500),
   MODIFY COLUMN id_card_no VARCHAR(500);
   ```

2. **数据加密迁移（使用注解自动加密）**
   ```java
   @Service
   public class NewTableMigrationService {

       @Autowired
       private PatientRepository patientRepository; // 原表Repository

       @Autowired
       private PatientNewRepository patientNewRepository; // 新表Repository

       /**
        * 使用JPA自动加密机制进行数据迁移
        * 只需要从原表查询数据并保存到新表，@Convert注解会自动处理加密
        */
       @Transactional
       public void migratePatientData() {
           int batchSize = 100; // 小批次，避免长事务
           int pageNumber = 0;

           while (true) {
               // 分页查询原表数据
               Pageable pageable = PageRequest.of(pageNumber, batchSize, Sort.by("id"));
               Page<Patient> patientPage = patientRepository.findAll(pageable);

               if (patientPage.isEmpty()) {
                   break;
               }

               // 转换并保存到新表（自动加密）
               List<PatientNew> newPatients = new ArrayList<>();
               for (Patient patient : patientPage.getContent()) {
                   PatientNew newPatient = new PatientNew();
                   newPatient.setId(patient.getId());
                   // 直接赋值，@Convert注解会自动处理加密
                   newPatient.setName(patient.getName());
                   newPatient.setPhone(patient.getPhone());
                   newPatient.setIdCardNo(patient.getIdCardNo());
                   newPatient.setAddress(patient.getAddress());
                   newPatients.add(newPatient);
               }

               // 批量保存到新表
               patientNewRepository.saveAll(newPatients);

               pageNumber++;

               // 控制处理速度
               try {
                   Thread.sleep(200);
               } catch (InterruptedException e) {
                   Thread.currentThread().interrupt();
                   break;
               }
           }
       }
   }

   // 新表实体类配置（使用直接加密模式）
   @Entity
   @Table(name = "ych_patient_new")
   public class PatientNew {
       @EncryptField(
           description = "患者姓名",
           migrationStrategy = MigrationStrategy.DIRECT_ENCRYPT,
           strategyKey = "name"
       )
       @Convert(converter = EncryptConverter.class)
       @Column(name = "name", length = 500)
       private String name;

       // 其他字段...
   }
   ```

3. **数据一致性验证**
   ```sql
   -- 验证数据条数一致性
   SELECT
       (SELECT COUNT(*) FROM ych_patient) as original_count,
       (SELECT COUNT(*) FROM ych_patient_new) as new_count;

   -- 验证关键字段非空
   SELECT COUNT(*) FROM ych_patient_new
   WHERE name IS NULL OR phone IS NULL OR id_card_no IS NULL;
   ```

4. **停服切换表名**
   ```bash
   # 1. 停止应用服务
   systemctl stop application-service

   # 2. 执行表重命名（原子操作）
   mysql -h rds-host -u username -p << EOF
   START TRANSACTION;

   -- 重命名原表为备份表
   RENAME TABLE
       ych_patient TO ych_patient_backup,
       ych_user TO ych_user_backup,
       ych_appoint_record TO ych_appoint_record_backup;

   -- 重命名新表为正式表
   RENAME TABLE
       ych_patient_new TO ych_patient,
       ych_user_new TO ych_user,
       ych_appoint_record_new TO ych_appoint_record;

   COMMIT;
   EOF

   # 3. 启动应用服务
   systemctl start application-service
   ```

**优势**：
- 切换过程极快（秒级）
- 原表作为备份保留
- 回滚简单快速

**劣势**：
- 需要双倍存储空间
- 迁移过程中数据可能不一致
- 需要处理外键约束

**预计停服时间**：2-6小时

##### 方案三：渐进式影子字段方案（统一加密组件的完整实现）

**技术原理**：
在现有表中添加影子字段，通过多种迁移策略实现渐进式迁移，支持配置驱动的策略管理。
**这是统一加密组件的完整实现，包含了所有加密注解、工具类、策略管理等核心功能。**

**核心特性**：
- 零停机迁移
- 多种迁移策略（明文优先、影子字段优先、仅影子字段）
- 配置驱动的策略管理
- 秒级快速回滚

**实施步骤**：

1. **添加影子字段**
   ```sql
   -- 为敏感字段添加加密字段
   ALTER TABLE ych_patient
   ADD COLUMN name_encrypted VARCHAR(500) COMMENT '姓名加密字段',
   ADD COLUMN phone_encrypted VARCHAR(500) COMMENT '手机号加密字段',
   ADD COLUMN id_card_no_encrypted VARCHAR(500) COMMENT '身份证号加密字段',
   ADD COLUMN address_encrypted VARCHAR(500) COMMENT '地址加密字段',
   ADD COLUMN data_version INT DEFAULT 1 COMMENT '数据版本',
   ADD COLUMN migration_status VARCHAR(20) DEFAULT 'NOT_MIGRATED' COMMENT '迁移状态';
   ```

2. **配置迁移策略**
   ```yaml
   # application.yml
   encryption:
     migration:
       default-strategy: PLAINTEXT_PRIORITY
       enable-global-override: false
       strategies:
         name: PLAINTEXT_PRIORITY      # 姓名：明文优先
         phone: PLAINTEXT_PRIORITY     # 手机：明文优先
         idCardNo: PLAINTEXT_PRIORITY  # 身份证：明文优先
         address: PLAINTEXT_PRIORITY   # 地址：明文优先
   ```

3. **实体类配置**
   ```java
   @Entity
   @Table(name = "ych_patient")
   public class Patient {
       @EncryptField(
           shadowField = "name_encrypted",
           migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY,
           strategyKey = "name"
       )
       private String name;

       @EncryptField(
           shadowField = "phone_encrypted",
           migrationStrategy = MigrationStrategy.PLAINTEXT_PRIORITY,
           strategyKey = "phone"
       )
       private String phone;

       // 影子字段
       @Column(name = "name_encrypted")
       private String nameEncrypted;

       @Column(name = "phone_encrypted")
       private String phoneEncrypted;
   }
   ```

4. **渐进式迁移执行**
   ```bash
   # 阶段1：部署应用（明文优先策略）
   # 新数据自动双写，读取仍使用明文

   # 阶段2：批量迁移存量数据
   curl -X POST http://localhost:8080/api/migration/migrate/2

   # 阶段3：切换到影子字段优先
   curl -X POST http://localhost:8080/api/strategy/switch/shadow

   # 阶段4：最终切换到仅影子字段
   curl -X POST http://localhost:8080/api/strategy/global \
     -H "Content-Type: application/json" \
     -d '{"strategy": "SHADOW_ONLY", "enableOverride": true}'

   # 阶段5：启动明文字段清理任务
   curl -X POST http://localhost:8080/api/migration/cleanup/start

   # 监控清理进度
   curl http://localhost:8080/api/migration/cleanup/status
   ```

5. **明文字段清理方案（基于xxl-job定时任务）**

   **清理策略**：
   - 使用xxl-job定时任务调度，确保清理过程可控
   - 按表逐一清理，避免对数据库造成过大压力
   - 每次只清理一部分数据，分批次执行
   - 支持暂停、恢复、回滚操作

   **技术实现**：
   ```java
   @Component
   @Slf4j
   public class PlaintextCleanupJobHandler {

       @Autowired
       private PlaintextCleanupService cleanupService;

       /**
        * xxl-job定时任务：清理明文字段
        * 执行参数格式：tableName=ych_patient,batchSize=100,delayMs=200
        */
       @XxlJob("plaintextCleanupJob")
       public void plaintextCleanupJob() {
           String param = XxlJobHelper.getJobParam();
           log.info("开始执行明文字段清理任务，参数：{}", param);

           try {
               // 解析任务参数
               Map<String, String> params = parseJobParams(param);
               String tableName = params.get("tableName");
               int batchSize = Integer.parseInt(params.getOrDefault("batchSize", "100"));
               int delayMs = Integer.parseInt(params.getOrDefault("delayMs", "200"));

               // 执行清理任务
               CleanupResult result = cleanupService.cleanupTable(tableName, batchSize, delayMs);

               if (result.isCompleted()) {
                   XxlJobHelper.handleSuccess("表 " + tableName + " 清理完成，共处理 " + result.getProcessedCount() + " 条记录");
               } else {
                   XxlJobHelper.handleSuccess("表 " + tableName + " 本批次处理完成，已处理 " + result.getProcessedCount() + " 条记录");
               }

           } catch (Exception e) {
               log.error("明文字段清理任务执行失败", e);
               XxlJobHelper.handleFail("清理任务执行失败：" + e.getMessage());
           }
       }
   }

   @Service
   @Slf4j
   public class PlaintextCleanupService {

       @Autowired
       private JdbcTemplate jdbcTemplate;

       @Autowired
       private RedisTemplate<String, Object> redisTemplate;

       private static final String CLEANUP_STATUS_KEY = "cleanup:status:";
       private static final String CLEANUP_PROGRESS_KEY = "cleanup:progress:";

       /**
        * 清理指定表的明文字段
        */
       @Transactional
       public CleanupResult cleanupTable(String tableName, int batchSize, int delayMs) {
           // 检查清理状态
           String statusKey = CLEANUP_STATUS_KEY + tableName;
           String status = (String) redisTemplate.opsForValue().get(statusKey);

           if ("PAUSED".equals(status)) {
               log.info("表 {} 的清理任务已暂停", tableName);
               return CleanupResult.paused();
           }

           // 获取清理进度
           String progressKey = CLEANUP_PROGRESS_KEY + tableName;
           Long lastProcessedId = (Long) redisTemplate.opsForValue().get(progressKey);
           if (lastProcessedId == null) {
               lastProcessedId = 0L;
           }

           // 根据表名选择清理策略
           switch (tableName) {
               case "ych_patient":
                   return cleanupPatientTable(lastProcessedId, batchSize, delayMs);
               case "ych_user":
                   return cleanupUserTable(lastProcessedId, batchSize, delayMs);
               case "ych_appoint_record":
                   return cleanupAppointRecordTable(lastProcessedId, batchSize, delayMs);
               default:
                   throw new IllegalArgumentException("不支持的表名：" + tableName);
           }
       }

       /**
        * 清理患者表明文字段
        */
       private CleanupResult cleanupPatientTable(Long lastProcessedId, int batchSize, int delayMs) {
           String tableName = "ych_patient";

           // 查询需要清理的记录
           String selectSql = "SELECT id FROM ych_patient " +
                             "WHERE id > ? AND migration_status = 'COMPLETED' " +
                             "AND (name IS NOT NULL OR phone IS NOT NULL OR id_card_no IS NOT NULL) " +
                             "ORDER BY id LIMIT ?";

           List<Long> ids = jdbcTemplate.queryForList(selectSql, Long.class, lastProcessedId, batchSize);

           if (ids.isEmpty()) {
               // 清理完成
               redisTemplate.delete(CLEANUP_PROGRESS_KEY + tableName);
               redisTemplate.delete(CLEANUP_STATUS_KEY + tableName);
               log.info("表 {} 明文字段清理完成", tableName);
               return CleanupResult.completed(0);
           }

           // 批量清理明文字段
           String updateSql = "UPDATE ych_patient SET " +
                             "name = NULL, " +
                             "phone = NULL, " +
                             "id_card_no = NULL, " +
                             "address = NULL, " +
                             "cleanup_time = NOW() " +
                             "WHERE id IN (" + String.join(",", Collections.nCopies(ids.size(), "?")) + ")";

           Object[] params = ids.toArray();
           int updatedCount = jdbcTemplate.update(updateSql, params);

           // 更新进度
           Long maxId = ids.get(ids.size() - 1);
           redisTemplate.opsForValue().set(CLEANUP_PROGRESS_KEY + tableName, maxId);

           // 控制清理速度
           if (delayMs > 0) {
               try {
                   Thread.sleep(delayMs);
               } catch (InterruptedException e) {
                   Thread.currentThread().interrupt();
               }
           }

           log.info("表 {} 本批次清理完成，处理记录数：{}，最大ID：{}", tableName, updatedCount, maxId);
           return CleanupResult.processing(updatedCount);
       }

       /**
        * 暂停清理任务
        */
       public void pauseCleanup(String tableName) {
           redisTemplate.opsForValue().set(CLEANUP_STATUS_KEY + tableName, "PAUSED");
           log.info("表 {} 的清理任务已暂停", tableName);
       }

       /**
        * 恢复清理任务
        */
       public void resumeCleanup(String tableName) {
           redisTemplate.delete(CLEANUP_STATUS_KEY + tableName);
           log.info("表 {} 的清理任务已恢复", tableName);
       }

       /**
        * 获取清理进度
        */
       public CleanupProgress getCleanupProgress(String tableName) {
           Long lastProcessedId = (Long) redisTemplate.opsForValue().get(CLEANUP_PROGRESS_KEY + tableName);
           String status = (String) redisTemplate.opsForValue().get(CLEANUP_STATUS_KEY + tableName);

           // 计算总记录数和已处理记录数
           String totalSql = "SELECT COUNT(*) FROM " + tableName + " WHERE migration_status = 'COMPLETED'";
           Long totalCount = jdbcTemplate.queryForObject(totalSql, Long.class);

           String processedSql = "SELECT COUNT(*) FROM " + tableName +
                               " WHERE migration_status = 'COMPLETED' AND cleanup_time IS NOT NULL";
           Long processedCount = jdbcTemplate.queryForObject(processedSql, Long.class);

           return CleanupProgress.builder()
                   .tableName(tableName)
                   .totalCount(totalCount)
                   .processedCount(processedCount)
                   .lastProcessedId(lastProcessedId)
                   .status(status != null ? status : "RUNNING")
                   .progressPercent(totalCount > 0 ? (processedCount * 100.0 / totalCount) : 0)
                   .build();
       }
   }
   ```

   **xxl-job任务配置**：
   ```yaml
   # xxl-job配置
   xxl:
     job:
       admin:
         addresses: http://xxl-job-admin:8080/xxl-job-admin
       executor:
         appname: dyt-data-encryption
         address:
         ip:
         port: 9999
         logpath: /data/applogs/xxl-job/jobhandler
         logretentiondays: 30
   ```

   **清理任务调度配置**：
   ```bash
   # 在xxl-job管理后台配置以下任务：

   # 任务1：清理患者表
   任务名称：患者表明文字段清理
   Cron表达式：0 */10 * * * ?  # 每10分钟执行一次
   执行器：dyt-data-encryption
   JobHandler：plaintextCleanupJob
   执行参数：tableName=ych_patient,batchSize=100,delayMs=200

   # 任务2：清理用户表
   任务名称：用户表明文字段清理
   Cron表达式：0 5 */1 * * ?   # 每小时的第5分钟执行
   执行器：dyt-data-encryption
   JobHandler：plaintextCleanupJob
   执行参数：tableName=ych_user,batchSize=50,delayMs=300

   # 任务3：清理预约记录表
   任务名称：预约记录表明文字段清理
   Cron表达式：0 15 */2 * * ?  # 每2小时的第15分钟执行
   执行器：dyt-data-encryption
   JobHandler：plaintextCleanupJob
   执行参数：tableName=ych_appoint_record,batchSize=200,delayMs=100
   ```

   **清理监控API**：
   ```java
   @RestController
   @RequestMapping("/api/migration/cleanup")
   @Slf4j
   public class CleanupController {

       @Autowired
       private PlaintextCleanupService cleanupService;

       /**
        * 启动清理任务
        */
       @PostMapping("/start")
       public ResponseEntity<String> startCleanup(@RequestParam String tableName) {
           try {
               // 通过xxl-job API启动任务
               // 这里需要调用xxl-job的API来启动对应的任务
               return ResponseEntity.ok("清理任务已启动：" + tableName);
           } catch (Exception e) {
               return ResponseEntity.status(500).body("启动清理任务失败：" + e.getMessage());
           }
       }

       /**
        * 暂停清理任务
        */
       @PostMapping("/pause")
       public ResponseEntity<String> pauseCleanup(@RequestParam String tableName) {
           cleanupService.pauseCleanup(tableName);
           return ResponseEntity.ok("清理任务已暂停：" + tableName);
       }

       /**
        * 恢复清理任务
        */
       @PostMapping("/resume")
       public ResponseEntity<String> resumeCleanup(@RequestParam String tableName) {
           cleanupService.resumeCleanup(tableName);
           return ResponseEntity.ok("清理任务已恢复：" + tableName);
       }

       /**
        * 查看清理进度
        */
       @GetMapping("/status")
       public ResponseEntity<Map<String, CleanupProgress>> getCleanupStatus() {
           Map<String, CleanupProgress> statusMap = new HashMap<>();
           statusMap.put("ych_patient", cleanupService.getCleanupProgress("ych_patient"));
           statusMap.put("ych_user", cleanupService.getCleanupProgress("ych_user"));
           statusMap.put("ych_appoint_record", cleanupService.getCleanupProgress("ych_appoint_record"));
           return ResponseEntity.ok(statusMap);
       }
   }
   ```

   **清理安全措施**：
   - **数据备份**：清理前自动备份明文数据到专用备份表
   - **分批处理**：每批次处理100-200条记录，避免长事务
   - **进度控制**：支持暂停、恢复、查看进度
   - **回滚机制**：保留备份数据，支持紧急回滚
   - **监控告警**：清理异常时自动告警

**优势**：
- 零停机迁移
- 秒级快速回滚
- 渐进式验证
- 配置驱动管理

**劣势**：
- 需要修改应用代码
- 增加存储空间
- 迁移周期较长

**预计停服时间**：0分钟

#### 5.3 三种方案对比分析

| 对比维度 | 方案一：备份数据库增量迁移 | 方案二：新表替换 | 方案三：渐进式影子字段 |
|---------|----------------------|----------------|-------------------|
| **停服时间** | 2-4小时 | 2-6小时 | 0分钟 |
| **技术复杂度** | 中等 | 简单 | 高 |
| **存储成本** | 高（需要备份数据库） | 高（双倍存储空间） | 中等（增加影子字段） |
| **回滚难度** | 高 | 低 | 极低 |
| **回滚时间** | 2-4小时 | 5-15分钟 | 秒级 |
| **数据一致性风险** | 低 | 中 | 极低 |
| **业务影响** | 高（长时间停服） | 中（短时间停服） | 极低（零停机） |
| **代码修改量** | 少 | 少 | 多 |
| **运维复杂度** | 高 | 中 | 低 |
| **验证周期** | 短 | 短 | 长 |
| **适用场景** | 数据量大，可接受停服 | 数据量中等，要求快速切换 | 核心业务，不能停服 |

#### 5.4 推荐方案

**综合评估结果**：推荐采用**方案三：渐进式影子字段方案**

**推荐理由**：
1. **业务连续性**：零停机迁移，不影响医院正常业务
2. **风险可控**：渐进式迁移，每个阶段都可验证和回滚
3. **快速响应**：秒级回滚能力，应急响应能力强
4. **技术先进**：配置驱动的策略管理，运维友好
5. **符合医疗行业特点**：医疗业务对连续性要求极高

**实施建议**：
- 优先实施方案三，作为主要技术路线
- 准备方案二作为应急备选方案
- 方案一可作为数据迁移的辅助手段

#### 5.5 应急回滚方案

##### 方案一应急回滚方案

**回滚触发条件**：
- 数据加密后发现业务功能异常
- 系统性能严重下降
- 数据一致性问题

**回滚步骤**：
```bash
# 1. 立即停止应用服务
systemctl stop application-service

# 2. 切换回原数据库
sed -i 's/backup-rds-host/prod-rds-host/g' application.yml

# 3. 恢复原数据库（如果已被修改）
mysql -h prod-rds-host -u username -p < original_backup_$(date +%Y%m%d).sql

# 4. 启动应用服务
systemctl start application-service

# 5. 验证服务正常
curl -f http://localhost:8080/health
```

**回滚时间**：2-4小时
**数据丢失风险**：中等（增量数据可能丢失）

##### 方案二应急回滚方案

**回滚触发条件**：
- 新表数据异常
- 应用无法正常访问新表
- 性能问题

**回滚步骤**：
```bash
# 1. 停止应用服务
systemctl stop application-service

# 2. 执行表回滚（原子操作）
mysql -h rds-host -u username -p << EOF
START TRANSACTION;

-- 删除新表
DROP TABLE ych_patient, ych_user, ych_appoint_record;

-- 恢复原表
RENAME TABLE
    ych_patient_backup TO ych_patient,
    ych_user_backup TO ych_user,
    ych_appoint_record_backup TO ych_appoint_record;

COMMIT;
EOF

# 3. 启动应用服务
systemctl start application-service
```

**回滚时间**：5-15分钟
**数据丢失风险**：低（原表完整保留）

##### 方案三应急回滚方案

**回滚触发条件**：
- 加密数据读取异常
- 性能问题
- 业务功能异常

**快速回滚（秒级）**：
```bash
# 1. 紧急回滚到明文优先模式
curl -X POST http://localhost:8080/api/strategy/rollback/plaintext

# 2. 验证回滚效果
curl http://localhost:8080/api/strategy/config
```

**完整回滚**：
```bash
# 1. 停止应用服务（可选）
systemctl stop application-service

# 2. 修改配置文件
cat > application.yml << EOF
encryption:
  migration:
    default-strategy: PLAINTEXT_PRIORITY
    enable-global-override: true
    strategies: {}
EOF

# 3. 启动应用服务
systemctl start application-service

# 4. 清理影子字段数据（可选）
mysql -h rds-host -u username -p << EOF
UPDATE ych_patient SET
    name_encrypted = NULL,
    phone_encrypted = NULL,
    id_card_no_encrypted = NULL,
    data_version = 1,
    migration_status = 'ROLLBACK';
EOF
```

**回滚时间**：秒级（快速回滚）/ 30分钟（完整回滚）
**数据丢失风险**：极低（明文数据完整保留）





#### 5.7 风险控制措施
##### 技术风险控制
1. **数据备份策略**
   - 每个阶段执行前进行全量备份
   - 实时增量备份机制
   - 多地域备份存储

2. **监控告警机制**
   ```yaml
   # 关键指标监控
   alerts:
     - name: "加密性能异常"
       condition: "response_time > 500ms"
       action: "立即告警"

     - name: "数据一致性异常"
       condition: "error_rate > 1%"
       action: "自动回滚"
   ```
3. **灰度发布策略**
   - 按用户群体分批发布
   - 按功能模块分批发布
   - 实时监控用户反馈

##### 业务风险控制
1. **业务连续性保障**
   - 核心业务功能优先保障
   - 非核心功能可降级处理
   - 应急处理预案

2. **用户体验保障**
   - 加密过程对用户透明
   - 响应时间不明显增加
   - 操作流程不变

3. **合规风险控制**
   - 严格按照数据安全法要求
   - 定期安全评估
   - 审计日志完整性保障

## 四、资源需求评估                |

### 工具与环境需求

| 类别   | 描述                                                              | 是否已有  |
| ---- | --------------------------------------------------------------- | ----- |
| 脱敏工具 | 使用现有工程中的脱敏工具类和实现<br>- 脱敏注解：SensitiveProperty.java<br>- 脱敏策略实现目录 | 是     |
| 短信服务 | 腾讯云短信服务(已集成)                                                    | 是     |
| 日志服务 | 阿里云SLS日志服务                                                      | 需配置接入 |
| 缓存服务 | Redis服务(用于Token管理和单点登录)                                         | 是     |
| 开发环境 | 测试(后台管理未部署)、生产环境                                                      | 是     |
| 加解密验证 | 复制数据库                                                      | 否     |


## 五、实施计划

### 详细工作分解

| 阶段&#xA;   | 任务模块&#xA;       | 子任务&#xA;      | 前端工作内容&#xA;                            | 前端工时（天）&#xA; | 后端工作内容&#xA;                                  | 后端工时（天）&#xA; | 漏洞修复对应关系&#xA;    |
| --------- | --------------- | ------------- | -------------------------------------- | ------------ | -------------------------------------------- | ------------ | ---------------- |
| 准备阶段&#xA; | 基础环境搭建&#xA;     | 测试库搭建&#xA;    | 无&#xA;                                 | 0&#xA;       | 复制现在测试数据库用来做数据安全整改开发&#xA;                            | 0.3&#xA;     | 无&#xA;           |
|           |                 | 日志系统集成&#xA;   | 无&#xA;                                 | 0&#xA;       | 接入阿里云 SLS 日志服务&#xA;                          | 0.2&#xA;     | 无&#xA;           |
| 开发阶段&#xA; | 密码安全策略&#xA;     | 密码强度校验&#xA;   | 实现密码输入框实时强度提示（正则校验）&#xA;               | 0.4&#xA;     | 实现密码强度校验（10-20 位，含特殊字符）&#xA;                 | 0.4&#xA;     | 漏洞 1&#xA;        |
|           |                 | 密码有效期管理&#xA;  | 登录时调用后端接口校验密码过期状态&#xA;                 | 0.3&#xA;     | 实现密码有效期管理（90 天过期机制）&#xA;                     | 0.8&#xA;       | 漏洞 1&#xA;        |
|           |                 | 过期密码处理&#xA;   | 实现过期 / 临期密码自动跳转修改页面&#xA;               | 0.7&#xA;     | 记录密码修改记录；历史密码查重；校验密码是否过期或临期&#xA;                         | 1.2&#xA;     | 漏洞 1&#xA;        |
|           |                 | 登录限制&#xA;     | 无&#xA;                                 | 0&#xA;       | 实现连续 5 次登录失败账户锁定&#xA;                        | 1&#xA;       | 漏洞 1&#xA;        |
|           |                 | 短信验证码&#xA;    | 实现短信验证码输入组件（倒计时功能）&#xA;                | 0.6&#xA;     | 实现登录短信验证码校验&#xA;                             | 1&#xA;       | 漏洞 1&#xA;        |
|           |                 | 单点登录&#xA;     | 无&#xA;                                 | 0&#xA;       | 基于 JWT+Redis 实现单点登录&#xA;                     | 1&#xA;       | 漏洞 8&#xA;        |
|           |                 | Token 管理&#xA; | 无&#xA;                                 | 0&#xA;       | 实现 token 有效期 60 分钟自动注销&#xA;                  | 0.4&#xA;     | 漏洞 1&#xA;        |
|           | 数据脱敏实现&#xA;     | 身份证号脱敏&#xA;   | 实现脱敏字段前端展示适配（如 “张 \*\*”），身份证照片显示需要而二次校验&#xA;          | 0.4&#xA;     | 实现病案复印身份证号脱敏（前 6 位 + 后 4 位）&#xA;             | 0.5&#xA;     | 漏洞 2/4/5/6&#xA;  |
|           |                 | 银行卡号和开户行脱敏&#xA;   | 实现脱敏字段前端展示适配（如 “139\*\*\*\*1234”）&#xA; | 0.1&#xA;     | 实现出院结算银行卡号脱敏（后 4 位）&#xA;                     | 0.3&#xA;     | 漏洞 2/4/5/6&#xA;  |
|           |                 | 手机号脱敏&#xA;    | 无&#xA;                                 | 0&#xA;       | 实现预约记录手机号脱敏（中间 4 位隐藏）&#xA;                   | 0.3&#xA;     | 漏洞 2/4/5/6&#xA;  |
|           |     访问控制强化     | 未脱敏数据访问权限管理&#xA;    | 开发敏感操作二次校验弹窗&#xA;                                 | 0.6&#xA;       | 实现从权限管理获取查看未脱敏数据校验和脱敏二次短信验证功能&#xA;                   | 1.5&#xA;     | 漏洞 2/4/5/6&#xA;  |
|           |                 | 数据导出控制&#xA;   | 开发敏感操作二次校验弹窗（如导出 / 查看原图）&#xA;          | 0&#xA;     | 实现数据导出二次校验（短信验证码 + 1000 条限制）&#xA;            | 1.5&#xA;     | 漏洞 3/4/5/6&#xA;  |
|           |                 | 敏感数据访问&#xA;   | 无&#xA;                                 | 0&#xA;       | 二次校验通过则返回未脱敏数据&#xA;                        | 1&#xA;       | 漏洞 3/4/5/6&#xA;  |
|           | 敏感数据加密存储&#xA;   | 加密实现&#xA;     | 无&#xA;                                 | 0&#xA;       | 实现 JPA Attribute Converter（AES-256-GCM）&#xA; | 1.5&#xA;     | 漏洞 10&#xA;       |
|           |                 | 存量数据处理&#xA;   | 无&#xA;                                 | 0&#xA;       | 编写存量数据加密脚本和验证&#xA;                              | 1&#xA;     | 漏洞 10&#xA;       |
|           |                 | 性能测试&#xA;     | 无&#xA;                                 | 0&#xA;       | 搭建加解密测试库验证性能&#xA;                            | 0.5&#xA;     | 漏洞 10&#xA;       |
|           | 审计日志系统&#xA;     | 日志记录&#xA;     | 无&#xA;                                 | 0&#xA;       | 通过 拦截器 记录操作日志（含请求参数、IP 地址、接口地址、时间、操作人）&#xA;              | 1&#xA;       | 漏洞 7&#xA;        |
|           |                 | 日志存储&#xA;     | 无&#xA;                                 | 0&#xA;       | 日志格式化后接入阿里云 SLS&#xA;                         | 1&#xA;       | 漏洞 7&#xA;        |
|           |                 | 查询页面&#xA;     | 开发后台审计查询页面（支持多条件查询）&#xA;               | 0.8&#xA;     | 无&#xA;                                       | 0&#xA;       | 漏洞 7&#xA;        |
|           | 单点登录与多因子认证&#xA; | 多因子认证&#xA;    | 无&#xA;                                 | 0&#xA;       | 登录与敏感操作增加短信验证码（腾讯云接口）&#xA;                   | 1&#xA;       | 漏洞 9&#xA;        |
| 测试阶段&#xA; | 全流程测试&#xA;      | 功能测试&#xA;     | 执行前端功能测试（密码校验、脱敏展示）&#xA;               | 1&#xA;       | 无&#xA;                                       | 0&#xA;       | 验证所有漏洞修复有效性&#xA; |
|           |                 | 安全测试&#xA;     | 无&#xA;                                 | 0&#xA;       | 执行后端安全测试（加密强度、权限绕过）&#xA;                     | 0.5&#xA;       | 验证所有漏洞修复有效性&#xA; |
|           |                 | 性能测试&#xA;     | 无&#xA;                                 | 0&#xA;       | 执行性能测试（加密解密延迟、审计日志写入效率）&#xA;                 | 0.5&#xA;       | 验证所有漏洞修复有效性&#xA; |
| 上线阶段&#xA; | 部署与回滚&#xA;      | 前端部署&#xA;     | 执行前端静态资源发布&#xA;                      | 0.5&#xA;     | 无&#xA;                                       | 0&#xA;       | 确保上线安全&#xA;      |
|           |                 | 生产数据库全量备份&#xA;     | 无&#xA;                                 | 0&#xA;       | 执行数据库全量备份&#xA;                           | 0.5&#xA;     | 确保上线安全&#xA;      |
|           |                 | 数据迁移&#xA;     | 无&#xA;                                 | 0&#xA;       | 执行数据库存量数据加密&#xA;                           | 0.5&#xA;     | 确保上线安全&#xA;      |
|           |                 | 监控与回滚&#xA;    | 无&#xA;                                 | 0&#xA;       | 全量发布后监控日志；&#xA;                      | &#xA;       | 确保上线安全&#xA;      |
| **总计**    | **前端工时总计**      | —&#xA;        | —&#xA;                                 | **5.4**      | —&#xA;                                       | —&#xA;       | —&#xA;           |
|           | **后端工时总计**      | —&#xA;        | —&#xA;                                 | —&#xA;       | —   &#xA;                                 | **20.9**       | —&#xA;           |
|           | **全阶段总计工时**     | —&#xA;        | —&#xA;                                 | **26.3**     | —&#xA;                                       | —&#xA;       | —&#xA;           |

## 六、验收标准与测试方案

### 安全验收标准


| **整改点** | **整改位置** | **验收标准** | **测试用例** | **优先级** | **备注** |
|------------|--------------|--------------|--------------|------------|----------|
| **1. 密码安全策略** | 前端/后端 | - 密码强度：10-20位，含大小写字母、数字、特殊字符<br>- 密码90天过期，7天内临期提示<br>- 连续5次登录失败锁定账户1小时<br>- 新密码不能与前3次相同<br>- 登录需短信验证码 | TC-PW-001~TC-PW-007 | 高 | 无白名单机制 |
| **2. 数据脱敏** | 病案复印、诊疗服务管理、出院结算等模块 | - 身份证号显示为`123456***1234`<br>- 手机号显示为`139****1234`<br>- 银行卡号显示为`************1234`<br>- 地址显示为`云南省**`<br>- 导出文件自动脱敏 | TC-DS-001~TC-DS-005 | 高 | 无白名单机制 |
| **3. 访问控制** | 敏感操作（查看未脱敏数据、导出） | - 敏感操作需通过短信验证码二次校验<br>- 二次校验有效期30分钟<br>- 无白名单，仅依赖动态授权 | TC-AC-001~TC-AC-008 | 高 | 无白名单机制 |
| **4. 单点登录** | 登录会话管理 | - 同一账号新登录使旧会话失效<br>- 会话超时30分钟自动注销 | TC-SSO-001~TC-SSO-002 | 高 | 无白名单机制 |
| **5. 多因子认证** | 登录与敏感操作 | - 登录需短信验证码<br>- 敏感操作需短信验证码<br>- 验证码频率限制（1分钟/次） | TC-MFA-001~TC-MFA-003 | 高 | 无白名单机制 |
| **6. 审计日志** | 操作记录 | - 记录用户、时间、操作、数据等信息<br>- 日志不可篡改，保存≥6个月<br>- 支持多条件查询 | TC-AL-001~TC-AL-003 | 高 | 无白名单机制 |
| **7. 敏感数据加密存储** | 数据库字段 | - 敏感数据字段加密存储（AES-256-GCM）<br>- 存量数据已加密 | TC-EC-001~TC-EC-003 | 高 | 无白名单机制 |
| **8. 业务兼容性** | 核心业务流程 | - 安全改造不影响挂号、缴费等核心功能<br>- 用户体验无明显下降 | TC-BU-001~TC-BU-002 | 高 | 无白名单机制 |
| **9. 性能与安全** | 登录、查询、日志写入 | - 200并发登录无异常<br>- 脱敏前后查询性能差异≤5%<br>- 日志写入≥1000条/秒 | TC-PT-001~TC-PT-003 | 中 | 无白名单机制 |
### 测试用例设计

### **测试用例编号与场景说明**
| **测试用例编号** | **测试场景** | **预期结果** | **备注** |
|------------------|--------------|--------------|----------|
| **TC-PW-001** | 输入10位密码（含大小写字母、数字、特殊字符） | 密码强度提示为“强”，允许提交 ||
| **TC-PW-002** | 输入8位密码（仅小写字母） | 密码强度提示为“弱”，禁止提交 ||
| **TC-PW-003** | 输入与前3次相同的密码 | 提示“新密码与历史密码重复”，禁止修改 ||
| **TC-PW-004** | 模拟90天过期账户登录 | 自动跳转至密码修改页面 ||
| **TC-PW-005** | 模拟7天内临期账户登录 | 提示“密码即将过期，请尽快修改” ||
| **TC-PW-006** | 连续5次输入错误密码 | 账户锁定1小时，提示“账户已锁定” ||
| **TC-PW-007** | 输入错误短信验证码登录 | 登录失败，提示“验证码错误” ||
| **TC-DS-001** | 查看病案复印模块的身份证号 | 显示为“123456***1234” ||
| **TC-DS-002** | 查看诊疗服务管理模块的手机号 | 显示为“139****1234” ||
| **TC-DS-003** | 查看出院结算模块的银行卡号 | 显示为“************1234” ||
| **TC-DS-004** | 查看预约记录的地址 | 显示为“云南省**” ||
| **TC-DS-005** | 导出病案复印数据文件 | 文件中身份证号显示为“123456***1234” ||
| **TC-AC-001** | 非授权用户查看病案复印身份证原图 | 弹出短信验证码弹窗，输入正确验证码后允许查看 ||
| **TC-AC-002** | 非授权用户导出病案复印数据 | 弹出短信验证码弹窗，输入正确验证码后允许导出 ||
| **TC-AC-003** | 普通用户查看出院结算银行卡号 | 显示为“************1234” ||
| **TC-AC-004** | 白名单用户查看病案复印身份证原图 | 直接显示身份证原图 |  |
| **TC-AC-005** | 授权用户输入正确验证码 | 操作成功，返回未脱敏数据 ||
| **TC-AC-006** | 授权用户输入错误验证码 | 操作失败，提示“验证码错误，返回未脱敏数据” ||
| **TC-AC-007** | 查看审计日志 | 验证敏感操作记录完整 ||
| **TC-AC-008** | 模拟越权访问敏感接口 | 返回403错误 ||
| **TC-SSO-001** | 同一账号在电脑和手机登录 | 电脑端登录后，手机端会话自动失效 ||
| **TC-SSO-002** | 登录后30分钟无操作 | 会话自动注销，需重新登录 ||
| **TC-MFA-001** | 登录时发送短信验证码 | 接收短信验证码，输入正确后登录成功 ||
| **TC-MFA-002** | 1分钟内重复请求验证码 | 提示“请稍后再试” ||
| **TC-MFA-003** | 输入错误验证码登录 | 登录失败，提示“验证码错误” ||
| **TC-AL-001** | 登录后查看审计日志 | 日志包含用户名、登录时间、IP地址 ||
| **TC-AL-002** | 查询某用户的操作日志 | 返回该用户的所有操作记录 ||
| **TC-AL-003** | 验证日志防篡改机制 | 修改日志内容后，系统检测到篡改并报警 ||
| **TC-EC-001** | 查询病案复印模块的身份证号字段 | 数据库中字段为密文 ||
| **TC-EC-002** | 查看病案复印页面的身份证号 | 显示为明文（已解密） ||
| **TC-EC-003** | 执行存量数据加密脚本 | 验证加密后数据与原始数据一致 ||
| **TC-BU-001** | 模拟用户挂号流程 | 流程顺利完成，无额外安全步骤干扰 ||
| **TC-BU-002** | 模拟用户缴费流程 | 流程顺利完成，无额外安全步骤干扰 ||
| **TC-PT-001** | 200用户同时登录 | 系统无崩溃，响应时间 ≤ 2秒 ||
| **TC-PT-002** | 查询1000条脱敏数据 | 响应时间 ≤ 1秒 ||
| **TC-PT-003** | 写入1000条审计日志 | 写入时间 ≤ 1秒 ||


### 业务兼容性验证方法

| 验证方面       | 验证方法                                                     | 关注点              | 责任人         |
| ---------- | -------------------------------------------------------- | ---------------- | ----------- |
| **功能影响评估** | - 业务流程图梳理与验证<br>- 关键业务场景测试<br>- 边界条件与异常场景测试              | 确保安全改造不影响核心业务功能  | 测试工程师、业务代表  |
| **用户体验评估** | - 操作步骤增加评估<br>- 响应时间监控<br>- 用户引导与提示设计评估                  | 确保安全措施对用户体验影响最小化 | 前端工程师、测试工程师 |
| **业务验收测试** | - 与业务部门共同制定业务验收测试计划<br>- 业务部门参与测试执行<br>- 收集业务部门反馈并进行必要调整 | 业务部门认可度和满意度      | 项目经理、业务代表   |

## 七、风险管理

### 可能的实施风险

| 风险类别       | 具体风险        | 可能性 | 影响程度 | 风险值 |
| ---------- | ----------- | --- | ---- | --- |
| **技术风险**   | 安全措施影响系统性能  | 中   | 高    | 中高  |
|            | 功能兼容性问题     | 高   | 中    | 中高  |
|            | 技术方案实施难度超预期 | 中   | 中    | 中   |
| **项目管理风险** | 时间进度延误      | 高   | 高    | 高   |
|            | 资源不足        | 中   | 高    | 中高  |
|            | 需求变更        | 中   | 中    | 中   |
| **业务影响风险** | **系统停机维护**      | 高   | 高    | 中高  |
|            | 用户体验下降      | 中   | 高    | 中高  |
|            | 业务流程变更      | 中   | 中    | 中   |
|            | 数据迁移风险      | 低   | 高    | 中   |
| **合规风险**   | 合规要求理解偏差    | 中   | 高    | 中高  |
|            | 安全措施不足      | 低   | 高    | 中   |
|            | 文档不完善       | 中   | 中    | 中   |

### 应急回滚方案

| 环节         | 措施                                                                |
| ---------- | ----------------------------------------------------------------- |
| **技术准备**   | - 生产环境部署前完整备份数据库和应用<br>- 准备快速回滚脚本和流程<br>- 建立独立的回滚环境                             | 上线前2天    |
| **回滚触发条件** | - 核心业务功能严重受影响<br>- 系统性能下降超过50%<br>- 出现数据安全事件或数据异常<br>- 安全功能存在严重缺陷 |
| **回滚流程**   | - 回滚决策审批流程<br>- 回滚执行步骤与角色分配<br>- 回滚后验证与确认流程<br>- 回滚时间窗口安排（夜间低峰时段） |
| **回滚后续措施** | - 问题分析与修复<br>- 修复后的功能重新测试流程<br>- 再次部署的计划制定                        |

### 业务连续性保障措施

| 保障类型        | 具体措施                                              |
| ----------- | ------------------------------------------------- |
| **灰度发布策略**  | - 分批次推进安全功能上线<br>- 先内部用户，后外部用户<br>- 先非核心功能，后核心功能  |
| **业务影响最小化** | - 在业务低峰期进行系统更新<br>- 提前公告系统维护时间<br>- 准备临时替代流程（如需要） |
| **应急响应机制**  | - 建立24小时应急响应团队<br>- 制定应急处理流程和指南<br>- 设立问题快速响应通道   |
| **监控与预警**   | - 实时监控系统性能指标<br>- 设置异常行为预警机制<br>- 建立用户反馈快速处理机制    |  全程执行    |

