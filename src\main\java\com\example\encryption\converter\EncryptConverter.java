package com.example.encryption.converter;

import com.example.encryption.util.AESGCMUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * JPA AttributeConverter实现
 * 用于在实体字段和数据库字段之间进行加密/解密转换
 * 
 * 该转换器会自动应用到所有使用@Convert(converter = EncryptConverter.class)注解的字段
 */
@Converter
public class EncryptConverter implements AttributeConverter<String, String> {
    
    private static final Logger logger = LoggerFactory.getLogger(EncryptConverter.class);
    
    /**
     * 将实体属性转换为数据库列值（加密）
     * 在保存到数据库之前调用
     * 
     * @param attribute 实体中的明文属性值
     * @return 加密后的数据库列值
     */
    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (!StringUtils.hasText(attribute)) {
            logger.debug("Attribute is null or empty, skipping encryption");
            return attribute;
        }
        
        try {
            String encrypted = AESGCMUtil.encrypt(attribute);
            logger.debug("Successfully converted attribute to database column (encrypted)");
            return encrypted;
        } catch (Exception e) {
            logger.error("Failed to encrypt attribute for database storage", e);
            // 在生产环境中，可能需要抛出异常或者有其他处理策略
            throw new RuntimeException("Failed to encrypt sensitive data", e);
        }
    }
    
    /**
     * 将数据库列值转换为实体属性（解密）
     * 在从数据库查询后调用
     * 
     * @param dbData 数据库中的加密列值
     * @return 解密后的实体属性值
     */
    @Override
    public String convertToEntityAttribute(String dbData) {
        if (!StringUtils.hasText(dbData)) {
            logger.debug("Database data is null or empty, skipping decryption");
            return dbData;
        }
        
        try {
            String decrypted = AESGCMUtil.decrypt(dbData);
            logger.debug("Successfully converted database column to entity attribute (decrypted)");
            return decrypted;
        } catch (Exception e) {
            logger.error("Failed to decrypt database data to entity attribute", e);
            // 在生产环境中，可能需要抛出异常或者有其他处理策略
            throw new RuntimeException("Failed to decrypt sensitive data", e);
        }
    }
}
