package com.example.encryption.controller;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.entity.EncryptionStrategy;
import com.example.encryption.service.DynamicStrategyService;
import com.example.encryption.util.EncryptionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 策略管理控制器
 * 提供动态策略配置的REST API接口
 */
@RestController
@RequestMapping("/api/strategies")
@CrossOrigin(origins = "*")
public class StrategyController {

    @Autowired
    private DynamicStrategyService dynamicStrategyService;

    /**
     * 获取所有策略配置
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllStrategies() {
        try {
            Map<String, EncryptionStrategy> strategies = dynamicStrategyService.getAllStrategies();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", strategies);
            response.put("count", strategies.size());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取策略配置失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取策略统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getStatistics() {
        try {
            Map<String, Object> stats = dynamicStrategyService.getStrategyStatistics();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 更新策略配置
     */
    @PostMapping("/update")
    public ResponseEntity<Map<String, Object>> updateStrategy(@RequestBody @Valid StrategyUpdateRequest request) {
        try {
            EncryptionStrategy strategy = dynamicStrategyService.updateStrategy(
                request.getStrategyKey(),
                request.getMigrationStrategy(),
                request.getAlgorithmType(),
                request.getDescription(),
                request.getUpdatedBy()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "策略配置更新成功");
            response.put("data", strategy);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新策略配置失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 更新全局策略
     */
    @PostMapping("/global")
    public ResponseEntity<Map<String, Object>> updateGlobalStrategy(@RequestBody @Valid GlobalStrategyRequest request) {
        try {
            dynamicStrategyService.updateGlobalStrategy(
                request.getGlobalStrategy(),
                request.isEnableGlobalOverride(),
                request.getUpdatedBy()
            );

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "全局策略更新成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新全局策略失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 刷新策略缓存
     */
    @PostMapping("/refresh")
    public ResponseEntity<Map<String, Object>> refreshCache() {
        try {
            dynamicStrategyService.refreshCache();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "策略缓存刷新成功");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "刷新缓存失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 获取支持的加密算法
     */
    @GetMapping("/algorithms")
    public ResponseEntity<Map<String, Object>> getSupportedAlgorithms() {
        try {
            EncryptionUtil.AlgorithmInfo[] algorithms = dynamicStrategyService.getSupportedAlgorithms();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", algorithms);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取算法信息失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 测试策略算法
     */
    @PostMapping("/test/{strategyKey}")
    public ResponseEntity<Map<String, Object>> testStrategy(@PathVariable String strategyKey) {
        try {
            boolean testResult = dynamicStrategyService.testStrategyAlgorithm(strategyKey);
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("testResult", testResult);
            response.put("message", testResult ? "算法测试成功" : "算法测试失败");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("testResult", false);
            response.put("message", "测试失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }

    /**
     * 批量测试所有策略算法
     */
    @PostMapping("/test-all")
    public ResponseEntity<Map<String, Object>> testAllStrategies() {
        try {
            Map<String, Boolean> testResults = dynamicStrategyService.testAllStrategiesAlgorithms();
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", testResults);
            response.put("message", "批量测试完成");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "批量测试失败: " + e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * 策略更新请求
     */
    public static class StrategyUpdateRequest {
        private String strategyKey;
        private EncryptField.MigrationStrategy migrationStrategy;
        private String algorithmType = "AES";
        private String description;
        private String updatedBy = "api";

        // Getters and Setters
        public String getStrategyKey() { return strategyKey; }
        public void setStrategyKey(String strategyKey) { this.strategyKey = strategyKey; }
        
        public EncryptField.MigrationStrategy getMigrationStrategy() { return migrationStrategy; }
        public void setMigrationStrategy(EncryptField.MigrationStrategy migrationStrategy) { this.migrationStrategy = migrationStrategy; }
        
        public String getAlgorithmType() { return algorithmType; }
        public void setAlgorithmType(String algorithmType) { this.algorithmType = algorithmType; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
        
        public String getUpdatedBy() { return updatedBy; }
        public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    }

    /**
     * 全局策略请求
     */
    public static class GlobalStrategyRequest {
        private EncryptField.MigrationStrategy globalStrategy;
        private boolean enableGlobalOverride;
        private String updatedBy = "api";

        // Getters and Setters
        public EncryptField.MigrationStrategy getGlobalStrategy() { return globalStrategy; }
        public void setGlobalStrategy(EncryptField.MigrationStrategy globalStrategy) { this.globalStrategy = globalStrategy; }
        
        public boolean isEnableGlobalOverride() { return enableGlobalOverride; }
        public void setEnableGlobalOverride(boolean enableGlobalOverride) { this.enableGlobalOverride = enableGlobalOverride; }
        
        public String getUpdatedBy() { return updatedBy; }
        public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }
    }
}
