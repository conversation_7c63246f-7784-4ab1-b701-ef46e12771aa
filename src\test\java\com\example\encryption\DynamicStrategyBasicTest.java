package com.example.encryption;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.entity.EncryptionStrategy;
import com.example.encryption.service.DynamicStrategyService;
import com.example.encryption.util.EncryptionUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 动态策略基础功能测试（不涉及SM2）
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class DynamicStrategyBasicTest {

    @Autowired
    private DynamicStrategyService dynamicStrategyService;

    @Test
    public void testAESEncryption() {
        System.out.println("=== 测试AES加密算法 ===");
        
        String originalText = "这是一个测试手机号：13812345678";
        System.out.println("原文: " + originalText);
        
        // 测试AES加密
        String encrypted = EncryptionUtil.encrypt(originalText, EncryptionUtil.AlgorithmType.AES);
        System.out.println("AES加密后: " + encrypted);
        assertNotNull(encrypted);
        assertNotEquals(originalText, encrypted);
        
        // 测试AES解密
        String decrypted = EncryptionUtil.decrypt(encrypted, EncryptionUtil.AlgorithmType.AES);
        System.out.println("AES解密后: " + decrypted);
        assertEquals(originalText, decrypted);
        
        System.out.println("AES加密测试通过！");
    }

    @Test
    public void testDynamicStrategyService() {
        System.out.println("=== 测试动态策略服务 ===");
        
        // 获取所有策略
        Map<String, EncryptionStrategy> strategies = dynamicStrategyService.getAllStrategies();
        System.out.println("当前策略数量: " + strategies.size());
        assertTrue(strategies.size() > 0);
        
        // 检查预设策略
        assertTrue(strategies.containsKey("phone"));
        assertTrue(strategies.containsKey("email"));
        
        // 验证手机号策略使用AES算法
        EncryptionStrategy phoneStrategy = strategies.get("phone");
        assertEquals("AES", phoneStrategy.getAlgorithmType());
        assertEquals(EncryptField.MigrationStrategy.DIRECT_ENCRYPT, phoneStrategy.getMigrationStrategy());
        
        System.out.println("手机号策略: " + phoneStrategy.getSummary());
        System.out.println("动态策略服务测试通过！");
    }

    @Test
    public void testDynamicStrategyUpdate() {
        System.out.println("=== 测试动态策略更新 ===");
        
        // 创建新策略
        String strategyKey = "testField";
        EncryptionStrategy newStrategy = dynamicStrategyService.updateStrategy(
            strategyKey,
            EncryptField.MigrationStrategy.DIRECT_ENCRYPT,
            "AES",
            "测试字段策略",
            "test"
        );
        
        assertNotNull(newStrategy);
        assertEquals(strategyKey, newStrategy.getStrategyKey());
        assertEquals("AES", newStrategy.getAlgorithmType());
        assertEquals(EncryptField.MigrationStrategy.DIRECT_ENCRYPT, newStrategy.getMigrationStrategy());
        
        // 验证策略已加载到缓存
        String algorithmType = dynamicStrategyService.getAlgorithmType(strategyKey);
        assertEquals("AES", algorithmType);
        
        // 更新策略
        EncryptionStrategy updatedStrategy = dynamicStrategyService.updateStrategy(
            strategyKey,
            EncryptField.MigrationStrategy.SHADOW_ONLY,
            "AES",
            "更新后的测试字段策略",
            "test"
        );
        
        assertEquals("AES", updatedStrategy.getAlgorithmType());
        assertEquals(EncryptField.MigrationStrategy.SHADOW_ONLY, updatedStrategy.getMigrationStrategy());
        
        System.out.println("动态策略更新测试通过！");
    }

    @Test
    public void testStrategyBasedEncryption() {
        System.out.println("=== 测试基于策略的加密 ===");
        
        String testData = "敏感数据测试";
        
        // 测试手机号策略加密（AES）
        String phoneEncrypted = dynamicStrategyService.encryptData(testData, "phone");
        System.out.println("手机号策略加密: " + phoneEncrypted);
        
        // 测试解密
        String phoneDecrypted = dynamicStrategyService.decryptData(phoneEncrypted, "phone");
        assertEquals(testData, phoneDecrypted);
        
        System.out.println("基于策略的加密测试通过！");
    }

    @Test
    public void testAlgorithmTesting() {
        System.out.println("=== 测试算法功能验证 ===");
        
        // 测试所有支持的算法
        EncryptionUtil.AlgorithmInfo[] algorithms = dynamicStrategyService.getSupportedAlgorithms();
        System.out.println("支持的算法数量: " + algorithms.length);
        
        for (EncryptionUtil.AlgorithmInfo algorithm : algorithms) {
            System.out.println("算法: " + algorithm.getName() + 
                             ", 显示名: " + algorithm.getDisplayName() + 
                             ", 支持: " + algorithm.isSupported() + 
                             ", 说明: " + algorithm.getNote());
        }
        
        // 测试AES策略算法
        boolean phoneTest = dynamicStrategyService.testStrategyAlgorithm("phone");
        System.out.println("手机号策略算法测试: " + phoneTest);
        assertTrue(phoneTest);
        
        // 批量测试（只测试AES算法的策略）
        Map<String, Boolean> allTests = dynamicStrategyService.testAllStrategiesAlgorithms();
        System.out.println("批量测试结果: " + allTests);
        
        // 验证AES策略都测试通过
        for (Map.Entry<String, Boolean> entry : allTests.entrySet()) {
            String strategyKey = entry.getKey();
            Boolean result = entry.getValue();
            String algorithmType = dynamicStrategyService.getAlgorithmType(strategyKey);
            
            if ("AES".equals(algorithmType)) {
                assertTrue(result, "AES策略 " + strategyKey + " 应该测试通过");
            }
        }
        
        System.out.println("算法功能验证测试通过！");
    }

    @Test
    public void testStrategyStatistics() {
        System.out.println("=== 测试策略统计信息 ===");
        
        Map<String, Object> stats = dynamicStrategyService.getStrategyStatistics();
        
        System.out.println("策略统计信息:");
        stats.forEach((key, value) -> {
            System.out.println("  " + key + ": " + value);
        });
        
        assertTrue((Integer) stats.get("totalStrategies") > 0);
        assertTrue((Long) stats.get("enabledStrategies") > 0);
        assertNotNull(stats.get("globalStrategy"));
        assertNotNull(stats.get("algorithmDistribution"));
        
        @SuppressWarnings("unchecked")
        Map<String, Long> algorithmDistribution = (Map<String, Long>) stats.get("algorithmDistribution");
        assertTrue(algorithmDistribution.containsKey("AES"));
        
        System.out.println("策略统计信息测试通过！");
    }

    @Test
    public void testGlobalStrategyOverride() {
        System.out.println("=== 测试全局策略覆盖 ===");
        
        // 测试当前策略
        EncryptField.MigrationStrategy currentStrategy = dynamicStrategyService.getStrategy("phone", EncryptField.MigrationStrategy.PLAINTEXT_PRIORITY);
        System.out.println("当前手机号策略: " + currentStrategy);
        
        // 启用全局策略覆盖
        dynamicStrategyService.updateGlobalStrategy(EncryptField.MigrationStrategy.SHADOW_ONLY, true, "test");
        
        // 验证全局策略生效
        EncryptField.MigrationStrategy overriddenStrategy = dynamicStrategyService.getStrategy("phone", EncryptField.MigrationStrategy.PLAINTEXT_PRIORITY);
        assertEquals(EncryptField.MigrationStrategy.SHADOW_ONLY, overriddenStrategy);
        
        // 恢复原设置
        dynamicStrategyService.updateGlobalStrategy(EncryptField.MigrationStrategy.PLAINTEXT_PRIORITY, false, "test");
        
        System.out.println("全局策略覆盖测试通过！");
    }

    @Test
    public void testAlgorithmSelection() {
        System.out.println("=== 测试算法选择功能 ===");
        
        // 测试算法类型解析
        EncryptionUtil.AlgorithmType aes = EncryptionUtil.parseAlgorithmType("AES");
        assertEquals(EncryptionUtil.AlgorithmType.AES, aes);
        
        EncryptionUtil.AlgorithmType defaultAlg = EncryptionUtil.parseAlgorithmType("UNKNOWN");
        assertEquals(EncryptionUtil.AlgorithmType.AES, defaultAlg); // 默认返回AES
        
        // 测试算法支持检查
        assertTrue(EncryptionUtil.isAlgorithmSupported(EncryptionUtil.AlgorithmType.AES));
        assertTrue(EncryptionUtil.isAlgorithmSupported(EncryptionUtil.AlgorithmType.SM2));
        assertFalse(EncryptionUtil.isAlgorithmSupported(EncryptionUtil.AlgorithmType.SM4));
        
        // 测试AES算法功能
        assertTrue(EncryptionUtil.testAlgorithm(EncryptionUtil.AlgorithmType.AES));
        
        System.out.println("算法选择功能测试通过！");
    }
}
