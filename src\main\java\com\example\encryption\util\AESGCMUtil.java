package com.example.encryption.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES-GCM加密工具类
 * 提供AES-256-GCM算法的加密和解密功能
 */
public class AESGCMUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(AESGCMUtil.class);
    
    private static final String ALGORITHM = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12; // 96 bits
    private static final int GCM_TAG_LENGTH = 16; // 128 bits
    
    // 默认密钥，实际使用中应该从环境变量或配置中心获取
    private static final String DEFAULT_SECRET_KEY = "MySecretKey123456789012345678901"; // 32 bytes for AES-256
    
    /**
     * 获取加密密钥
     * 优先从环境变量获取，如果没有则使用默认密钥
     */
    private static String getSecretKey() {
        String key = System.getenv("AES_SECRET_KEY");
        if (!StringUtils.hasText(key)) {
            key = System.getProperty("encryption.secret-key", DEFAULT_SECRET_KEY);
        }
        
        // 确保密钥长度为32字节（AES-256）
        if (key.length() < 32) {
            key = String.format("%-32s", key).replace(' ', '0');
        } else if (key.length() > 32) {
            key = key.substring(0, 32);
        }
        
        return key;
    }
    
    /**
     * 加密字符串
     * @param plainText 明文
     * @return 加密后的Base64编码字符串
     */
    public static String encrypt(String plainText) {
        if (!StringUtils.hasText(plainText)) {
            return plainText;
        }
        
        try {
            // 生成随机IV
            SecureRandom secureRandom = new SecureRandom();
            byte[] iv = new byte[GCM_IV_LENGTH];
            secureRandom.nextBytes(iv);
            
            // 创建密钥
            SecretKeySpec secretKey = new SecretKeySpec(
                getSecretKey().getBytes(StandardCharsets.UTF_8), "AES");
            
            // 初始化加密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv));
            
            // 加密数据
            byte[] encryptedBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
            
            // 将IV和加密后的数据合并
            ByteBuffer byteBuffer = ByteBuffer.allocate(iv.length + encryptedBytes.length);
            byteBuffer.put(iv);
            byteBuffer.put(encryptedBytes);
            byte[] encryptedIvAndText = byteBuffer.array();
            
            // 转换为Base64编码
            String result = Base64.getEncoder().encodeToString(encryptedIvAndText);
            logger.debug("Successfully encrypted data, original length: {}, encrypted length: {}", 
                plainText.length(), result.length());
            
            return result;
        } catch (Exception e) {
            logger.error("Failed to encrypt data", e);
            throw new RuntimeException("加密失败", e);
        }
    }
    
    /**
     * 解密字符串
     * @param encryptedText 加密的Base64编码字符串
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedText) {
        if (!StringUtils.hasText(encryptedText)) {
            return encryptedText;
        }
        
        try {
            // 解码Base64数据
            byte[] encryptedIvTextBytes = Base64.getDecoder().decode(encryptedText);
            
            // 分离IV和加密数据
            ByteBuffer byteBuffer = ByteBuffer.wrap(encryptedIvTextBytes);
            byte[] iv = new byte[GCM_IV_LENGTH];
            byteBuffer.get(iv);
            byte[] cipherBytes = new byte[byteBuffer.remaining()];
            byteBuffer.get(cipherBytes);
            
            // 创建密钥
            SecretKeySpec secretKey = new SecretKeySpec(
                getSecretKey().getBytes(StandardCharsets.UTF_8), "AES");
            
            // 初始化解密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv));
            
            // 解密数据
            byte[] decryptedBytes = cipher.doFinal(cipherBytes);
            
            String result = new String(decryptedBytes, StandardCharsets.UTF_8);
            logger.debug("Successfully decrypted data, encrypted length: {}, decrypted length: {}", 
                encryptedText.length(), result.length());
            
            return result;
        } catch (Exception e) {
            logger.error("Failed to decrypt data", e);
            throw new RuntimeException("解密失败", e);
        }
    }
}
