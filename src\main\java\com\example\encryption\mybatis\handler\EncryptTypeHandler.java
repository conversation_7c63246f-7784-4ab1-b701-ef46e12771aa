package com.example.encryption.mybatis.handler;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.util.AESGCMUtil;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MyBatis加密TypeHandler
 * 用于在SQL执行过程中自动加密/解密标注了@EncryptField的字段
 */
@MappedTypes(String.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class EncryptTypeHandler extends BaseTypeHandler<String> {
    
    private static final Logger logger = LoggerFactory.getLogger(EncryptTypeHandler.class);
    
    // 缓存字段是否需要加密的信息，提高性能
    private static final Map<String, Boolean> fieldEncryptionCache = new ConcurrentHashMap<>();
    
    // 当前处理的字段信息，使用ThreadLocal确保线程安全
    private static final ThreadLocal<FieldContext> currentFieldContext = new ThreadLocal<>();
    
    /**
     * 字段上下文信息
     */
    public static class FieldContext {
        private final String entityClass;
        private final String fieldName;
        
        public FieldContext(String entityClass, String fieldName) {
            this.entityClass = entityClass;
            this.fieldName = fieldName;
        }
        
        public String getEntityClass() {
            return entityClass;
        }
        
        public String getFieldName() {
            return fieldName;
        }
        
        public String getCacheKey() {
            return entityClass + "." + fieldName;
        }
    }
    
    /**
     * 设置当前字段上下文（在Mapper中调用）
     */
    public static void setFieldContext(String entityClass, String fieldName) {
        currentFieldContext.set(new FieldContext(entityClass, fieldName));
    }
    
    /**
     * 清除当前字段上下文
     */
    public static void clearFieldContext() {
        currentFieldContext.remove();
    }
    
    /**
     * 设置非空参数到PreparedStatement（加密）
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        if (shouldEncryptCurrentField()) {
            logger.debug("Encrypting parameter for index: {}", i);
            ps.setString(i, AESGCMUtil.encrypt(parameter));
        } else {
            ps.setString(i, parameter);
        }
    }
    
    /**
     * 从ResultSet获取结果（解密）
     */
    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return processValue(value, columnName);
    }
    
    /**
     * 从ResultSet获取结果（解密）
     */
    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        // 无法获取字段名，尝试从上下文获取
        return processValueWithContext(value);
    }
    
    /**
     * 从CallableStatement获取结果（解密）
     */
    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        // 无法获取字段名，尝试从上下文获取
        return processValueWithContext(value);
    }
    
    /**
     * 处理值（根据字段名判断是否需要解密）
     */
    private String processValue(String value, String columnName) {
        if (!StringUtils.hasText(value) || !StringUtils.hasText(columnName)) {
            return value;
        }
        
        // 将数据库列名转换为字段名（假设使用下划线命名转驼峰命名）
        String fieldName = convertColumnNameToFieldName(columnName);
        
        if (shouldEncryptField(fieldName)) {
            logger.debug("Decrypting value for column: {}", columnName);
            return AESGCMUtil.decrypt(value);
        }
        
        return value;
    }
    
    /**
     * 使用上下文信息处理值
     */
    private String processValueWithContext(String value) {
        if (!StringUtils.hasText(value)) {
            return value;
        }
        
        if (shouldEncryptCurrentField()) {
            logger.debug("Decrypting value using field context");
            return AESGCMUtil.decrypt(value);
        }
        
        return value;
    }
    
    /**
     * 判断当前字段是否需要加密
     */
    private boolean shouldEncryptCurrentField() {
        FieldContext context = currentFieldContext.get();
        if (context == null) {
            return false;
        }
        
        return shouldEncryptField(context.getEntityClass(), context.getFieldName());
    }
    
    /**
     * 判断指定字段是否需要加密
     */
    private boolean shouldEncryptField(String fieldName) {
        FieldContext context = currentFieldContext.get();
        if (context == null) {
            return false;
        }
        
        return shouldEncryptField(context.getEntityClass(), fieldName);
    }
    
    /**
     * 判断指定类的字段是否需要加密
     */
    private boolean shouldEncryptField(String className, String fieldName) {
        if (!StringUtils.hasText(className) || !StringUtils.hasText(fieldName)) {
            return false;
        }
        
        String cacheKey = className + "." + fieldName;
        
        // 从缓存中获取
        if (fieldEncryptionCache.containsKey(cacheKey)) {
            return fieldEncryptionCache.get(cacheKey);
        }
        
        try {
            Class<?> clazz = Class.forName(className);
            Field field = clazz.getDeclaredField(fieldName);
            
            boolean encrypted = field.isAnnotationPresent(EncryptField.class);
            fieldEncryptionCache.put(cacheKey, encrypted);
            
            logger.debug("Field {}.{} encryption status: {}", className, fieldName, encrypted);
            return encrypted;
        } catch (Exception e) {
            logger.warn("Failed to check encryption status for field {}.{}: {}", className, fieldName, e.getMessage());
            fieldEncryptionCache.put(cacheKey, false);
            return false;
        }
    }
    
    /**
     * 将数据库列名转换为Java字段名（下划线转驼峰）
     */
    private String convertColumnNameToFieldName(String columnName) {
        if (!StringUtils.hasText(columnName)) {
            return columnName;
        }
        
        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        
        for (char c : columnName.toCharArray()) {
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(Character.toLowerCase(c));
                }
            }
        }
        
        return result.toString();
    }
}
