package com.example.encryption.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 敏感字段加密注解
 * 用于标记需要在数据库中加密存储的字段
 * 支持渐进式影子字段迁移策略
 *
 * 使用示例：
 * <pre>
 * {@code
 * @Entity
 * public class User {
 *     // 传统加密方式
 *     @EncryptField
 *     private String phone;
 *
 *     // 影子字段迁移方式
 *     @EncryptField(shadowField = "phone_encrypted", migrationStrategy = MigrationStrategy.SHADOW_PRIORITY)
 *     private String email;
 *
 *     // 影子字段（数据库字段，不在实体中使用）
 *     private String phone_encrypted;
 * }
 * }
 * </pre>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface EncryptField {

    /**
     * 字段描述，用于日志记录和调试
     * @return 字段描述
     */
    String description() default "";

    /**
     * 是否启用加密，默认为true
     * 可以通过配置动态控制是否加密
     * @return 是否启用加密
     */
    boolean enabled() default true;

    /**
     * 影子字段名称
     * 用于渐进式迁移，指定对应的加密字段名
     * 如果为空，则使用传统的直接加密方式
     * @return 影子字段名称
     */
    String shadowField() default "";

    /**
     * 迁移策略
     * 如果配置了全局策略，则优先使用全局策略
     * @return 迁移策略
     */
    MigrationStrategy migrationStrategy() default MigrationStrategy.DIRECT_ENCRYPT;

    /**
     * 策略配置键
     * 用于从application.yml中读取策略配置
     * 格式：encryption.migration.strategies.{strategyKey}
     * @return 策略配置键
     */
    String strategyKey() default "";

    /**
     * 数据版本号
     * 用于标识数据的加密版本，便于后续升级和回滚
     * @return 版本号
     */
    int version() default 1;

    /**
     * 迁移策略枚举
     */
    enum MigrationStrategy {
        /**
         * 直接加密模式（传统模式）
         * 直接在当前字段上进行加密存储
         */
        DIRECT_ENCRYPT,

        /**
         * 明文字段优先模式
         * 读取时优先从明文字段读取，如果为空则从影子字段读取
         * 写入时同时写入明文字段和影子字段
         * 适用于迁移初期，保证数据安全的同时支持快速回滚
         */
        PLAINTEXT_PRIORITY,

        /**
         * 影子字段优先模式
         * 读取时优先从影子字段读取，如果为空则从明文字段读取
         * 写入时只写入影子字段，明文字段置空
         * 适用于迁移中期，逐步切换到加密存储
         */
        SHADOW_PRIORITY,

        /**
         * 仅影子字段模式
         * 只使用影子字段进行读写，完全忽略明文字段
         * 适用于迁移完成后，完全使用加密存储
         */
        SHADOW_ONLY
    }
}
