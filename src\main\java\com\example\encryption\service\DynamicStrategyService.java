package com.example.encryption.service;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.config.MigrationStrategyConfig;
import com.example.encryption.entity.EncryptionStrategy;
import com.example.encryption.repository.EncryptionStrategyRepository;
import com.example.encryption.util.EncryptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 动态策略管理服务
 * 提供策略配置的动态加载、更新和持久化功能
 */
@Service
@Slf4j
public class DynamicStrategyService {

    @Autowired
    private EncryptionStrategyRepository strategyRepository;

    @Autowired
    private MigrationStrategyConfig migrationStrategyConfig;

    /**
     * 内存缓存，用于快速访问策略配置
     */
    private final Map<String, EncryptionStrategy> strategyCache = new ConcurrentHashMap<>();

    /**
     * 全局策略配置
     */
    private volatile EncryptField.MigrationStrategy globalStrategy;
    private volatile boolean globalOverrideEnabled;

    @PostConstruct
    public void initializeStrategies() {
        log.info("初始化动态策略配置...");
        
        // 从数据库加载策略配置
        loadStrategiesFromDatabase();
        
        // 如果数据库为空，从配置文件初始化
        if (strategyCache.isEmpty()) {
            initializeFromConfig();
        }
        
        // 初始化全局策略
        this.globalStrategy = migrationStrategyConfig.getDefaultStrategy();
        this.globalOverrideEnabled = migrationStrategyConfig.isEnableGlobalOverride();
        
        log.info("动态策略配置初始化完成，加载了 {} 个策略配置", strategyCache.size());
    }

    /**
     * 从数据库加载策略配置
     */
    private void loadStrategiesFromDatabase() {
        try {
            List<EncryptionStrategy> strategies = strategyRepository.findByEnabledTrueOrderByPriorityAsc();
            strategyCache.clear();
            
            for (EncryptionStrategy strategy : strategies) {
                strategyCache.put(strategy.getStrategyKey(), strategy);
                log.debug("加载策略配置: {}", strategy.getSummary());
            }
            
            log.info("从数据库加载了 {} 个策略配置", strategies.size());
        } catch (Exception e) {
            log.warn("从数据库加载策略配置失败，将使用配置文件: {}", e.getMessage());
        }
    }

    /**
     * 从配置文件初始化策略配置到数据库
     */
    @Transactional
    public void initializeFromConfig() {
        log.info("从配置文件初始化策略配置到数据库...");
        
        Map<String, EncryptField.MigrationStrategy> configStrategies = migrationStrategyConfig.getAllStrategies();
        
        for (Map.Entry<String, EncryptField.MigrationStrategy> entry : configStrategies.entrySet()) {
            String strategyKey = entry.getKey();
            EncryptField.MigrationStrategy strategy = entry.getValue();
            
            // 检查是否已存在
            if (!strategyRepository.existsByStrategyKey(strategyKey)) {
                EncryptionStrategy encryptionStrategy = new EncryptionStrategy(
                    strategyKey, 
                    strategy, 
                    "从配置文件初始化: " + strategyKey
                );
                encryptionStrategy.setCreatedBy("system");
                encryptionStrategy.setUpdatedBy("system");
                
                strategyRepository.save(encryptionStrategy);
                strategyCache.put(strategyKey, encryptionStrategy);
                
                log.info("初始化策略配置: {} -> {}", strategyKey, strategy);
            }
        }
    }

    /**
     * 获取字段的迁移策略
     * 
     * @param strategyKey 策略键
     * @param annotationStrategy 注解中的策略
     * @return 最终使用的策略
     */
    public EncryptField.MigrationStrategy getStrategy(String strategyKey, EncryptField.MigrationStrategy annotationStrategy) {
        // 如果启用了全局策略覆盖，直接返回全局策略
        if (globalOverrideEnabled) {
            return globalStrategy;
        }
        
        // 优先从缓存中获取
        if (strategyKey != null && !strategyKey.trim().isEmpty()) {
            EncryptionStrategy strategy = strategyCache.get(strategyKey);
            if (strategy != null && strategy.isValid()) {
                return strategy.getMigrationStrategy();
            }
        }
        
        // 回退到注解中的策略
        return annotationStrategy;
    }

    /**
     * 获取字段的加密算法类型
     * 
     * @param strategyKey 策略键
     * @return 加密算法类型
     */
    public String getAlgorithmType(String strategyKey) {
        if (strategyKey != null && !strategyKey.trim().isEmpty()) {
            EncryptionStrategy strategy = strategyCache.get(strategyKey);
            if (strategy != null && strategy.isValid()) {
                return strategy.getAlgorithmType();
            }
        }
        return "AES"; // 默认算法
    }

    /**
     * 动态更新策略配置
     * 
     * @param strategyKey 策略键
     * @param migrationStrategy 迁移策略
     * @param algorithmType 算法类型
     * @param description 描述
     * @param updatedBy 更新者
     * @return 更新后的策略配置
     */
    @Transactional
    @CacheEvict(value = "strategies", key = "#strategyKey")
    public EncryptionStrategy updateStrategy(String strategyKey, 
                                           EncryptField.MigrationStrategy migrationStrategy,
                                           String algorithmType,
                                           String description, 
                                           String updatedBy) {
        log.info("动态更新策略配置: {} -> {}, 算法: {}", strategyKey, migrationStrategy, algorithmType);
        
        Optional<EncryptionStrategy> existingOpt = strategyRepository.findByStrategyKey(strategyKey);
        EncryptionStrategy strategy;
        
        if (existingOpt.isPresent()) {
            // 更新现有策略
            strategy = existingOpt.get();
            strategy.setMigrationStrategy(migrationStrategy);
            strategy.setAlgorithmType(algorithmType);
            strategy.setDescription(description);
            strategy.setUpdatedBy(updatedBy);
        } else {
            // 创建新策略
            strategy = new EncryptionStrategy(strategyKey, migrationStrategy, algorithmType, description);
            strategy.setCreatedBy(updatedBy);
            strategy.setUpdatedBy(updatedBy);
        }
        
        strategy = strategyRepository.save(strategy);
        
        // 更新缓存
        strategyCache.put(strategyKey, strategy);
        
        // 同步更新配置对象（保持向后兼容）
        migrationStrategyConfig.updateStrategy(strategyKey, migrationStrategy);
        
        log.info("策略配置更新成功: {}", strategy.getSummary());
        return strategy;
    }

    /**
     * 更新全局策略
     * 
     * @param globalStrategy 全局策略
     * @param enableGlobalOverride 是否启用全局覆盖
     * @param updatedBy 更新者
     */
    @Transactional
    public void updateGlobalStrategy(EncryptField.MigrationStrategy globalStrategy, 
                                   boolean enableGlobalOverride, 
                                   String updatedBy) {
        log.info("更新全局策略: {} -> {}, 全局覆盖: {}", this.globalStrategy, globalStrategy, enableGlobalOverride);
        
        this.globalStrategy = globalStrategy;
        this.globalOverrideEnabled = enableGlobalOverride;
        
        // 同步更新配置对象
        migrationStrategyConfig.updateGlobalStrategy(globalStrategy);
        migrationStrategyConfig.setGlobalOverrideEnabled(enableGlobalOverride);
        
        log.info("全局策略更新成功");
    }

    /**
     * 刷新策略缓存
     */
    @CacheEvict(value = "strategies", allEntries = true)
    public void refreshCache() {
        log.info("刷新策略缓存...");
        loadStrategiesFromDatabase();
        log.info("策略缓存刷新完成");
    }

    /**
     * 获取所有策略配置
     * 
     * @return 策略配置映射
     */
    @Cacheable(value = "strategies", key = "'all'")
    public Map<String, EncryptionStrategy> getAllStrategies() {
        return new HashMap<>(strategyCache);
    }

    /**
     * 获取策略统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getStrategyStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalStrategies", strategyCache.size());
        stats.put("enabledStrategies", strategyRepository.countEnabledStrategies());
        stats.put("globalStrategy", globalStrategy);
        stats.put("globalOverrideEnabled", globalOverrideEnabled);
        
        // 按算法类型统计
        Map<String, Long> algorithmStats = new HashMap<>();
        strategyCache.values().forEach(strategy -> {
            String algorithm = strategy.getAlgorithmType();
            algorithmStats.put(algorithm, algorithmStats.getOrDefault(algorithm, 0L) + 1);
        });
        stats.put("algorithmDistribution", algorithmStats);
        
        return stats;
    }

    /**
     * 检查策略配置是否有效
     *
     * @param strategyKey 策略键
     * @return 是否有效
     */
    public boolean isStrategyValid(String strategyKey) {
        EncryptionStrategy strategy = strategyCache.get(strategyKey);
        return strategy != null && strategy.isValid();
    }

    /**
     * 根据策略键加密数据
     *
     * @param plaintext 明文
     * @param strategyKey 策略键
     * @return 加密后的密文
     */
    public String encryptData(String plaintext, String strategyKey) {
        String algorithmType = getAlgorithmType(strategyKey);
        return EncryptionUtil.encrypt(plaintext, algorithmType);
    }

    /**
     * 根据策略键解密数据
     *
     * @param ciphertext 密文
     * @param strategyKey 策略键
     * @return 解密后的明文
     */
    public String decryptData(String ciphertext, String strategyKey) {
        String algorithmType = getAlgorithmType(strategyKey);
        return EncryptionUtil.decrypt(ciphertext, algorithmType);
    }

    /**
     * 测试指定策略的加密算法
     *
     * @param strategyKey 策略键
     * @return 测试结果
     */
    public boolean testStrategyAlgorithm(String strategyKey) {
        try {
            String algorithmType = getAlgorithmType(strategyKey);
            EncryptionUtil.AlgorithmType algorithm = EncryptionUtil.parseAlgorithmType(algorithmType);
            return EncryptionUtil.testAlgorithm(algorithm);
        } catch (Exception e) {
            log.error("测试策略{}的算法失败", strategyKey, e);
            return false;
        }
    }

    /**
     * 获取所有支持的加密算法信息
     *
     * @return 算法信息数组
     */
    public EncryptionUtil.AlgorithmInfo[] getSupportedAlgorithms() {
        return EncryptionUtil.getSupportedAlgorithms();
    }

    /**
     * 批量测试所有策略的算法
     *
     * @return 测试结果映射
     */
    public Map<String, Boolean> testAllStrategiesAlgorithms() {
        Map<String, Boolean> results = new HashMap<>();

        for (String strategyKey : strategyCache.keySet()) {
            boolean testResult = testStrategyAlgorithm(strategyKey);
            results.put(strategyKey, testResult);
        }

        log.info("批量测试完成，共测试{}个策略", results.size());
        return results;
    }
}
