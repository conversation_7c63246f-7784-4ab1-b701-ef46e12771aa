package com.example.encryption.mybatis.entity;

import com.example.encryption.annotation.EncryptField;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * MyBatis用户实体类
 * 演示MyBatis环境下的敏感字段加密存储
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MybatisUser {
    
    private Long id;
    
    /**
     * 用户名 - 不加密
     */
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    /**
     * 手机号 - 敏感信息，需要加密存储
     */
    @EncryptField(description = "用户手机号")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 邮箱 - 敏感信息，需要加密存储
     */
    @EncryptField(description = "用户邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 身份证号 - 敏感信息，需要加密存储
     */
    @EncryptField(description = "身份证号")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;
    
    /**
     * 真实姓名 - 敏感信息，需要加密存储
     */
    @EncryptField(description = "真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    private String realName;
    
    /**
     * 年龄 - 不敏感，不加密
     */
    private Integer age;
    
    /**
     * 用户状态 - 不敏感，不加密
     */
    private UserStatus status = UserStatus.ACTIVE;
    
    /**
     * 创建时间 - 不敏感，不加密
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间 - 不敏感，不加密
     */
    private LocalDateTime updatedAt;
    
    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        ACTIVE("激活"),
        INACTIVE("未激活"),
        SUSPENDED("暂停"),
        DELETED("已删除");
        
        private final String description;
        
        UserStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 构造函数 - 用于测试
     */
    public MybatisUser(String username, String phone, String email, String idCard, String realName, Integer age) {
        this.username = username;
        this.phone = phone;
        this.email = email;
        this.idCard = idCard;
        this.realName = realName;
        this.age = age;
        this.status = UserStatus.ACTIVE;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
