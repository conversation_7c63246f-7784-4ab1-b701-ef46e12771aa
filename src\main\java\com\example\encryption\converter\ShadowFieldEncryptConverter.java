package com.example.encryption.converter;

import com.example.encryption.annotation.EncryptField;
import com.example.encryption.migration.ShadowFieldMigrationManager;
import com.example.encryption.util.AESGCMUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.lang.reflect.Field;

/**
 * 支持影子字段迁移的JPA属性转换器
 * 
 * 功能特性：
 * 1. 支持传统的直接加密模式
 * 2. 支持渐进式影子字段迁移
 * 3. 根据注解配置自动选择处理策略
 * 4. 提供向后兼容性
 */
@Converter
@Component
public class ShadowFieldEncryptConverter implements AttributeConverter<String, String> {
    
    private static final Logger logger = LoggerFactory.getLogger(ShadowFieldEncryptConverter.class);
    
    @Autowired(required = false)
    private ShadowFieldMigrationManager migrationManager;
    
    /**
     * 将实体属性转换为数据库列值（加密）
     * 在保存到数据库之前调用
     * 
     * @param attribute 实体中的明文属性值
     * @return 加密后的数据库列值
     */
    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (!StringUtils.hasText(attribute)) {
            logger.debug("Attribute is null or empty, skipping encryption");
            return attribute;
        }
        
        try {
            // 获取当前字段的加密配置
            FieldEncryptionContext context = getCurrentFieldContext();
            
            if (context == null || !context.isEncrypted()) {
                // 非加密字段或无法获取上下文，直接返回原值
                logger.debug("Field is not encrypted or context unavailable, returning original value");
                return attribute;
            }
            
            // 检查是否启用影子字段迁移
            if (context.hasShadowField() && migrationManager != null) {
                // 使用影子字段迁移管理器处理
                logger.debug("Using shadow field migration for encryption");
                return handleShadowFieldEncryption(attribute, context);
            } else {
                // 使用传统的直接加密方式
                logger.debug("Using direct encryption");
                return AESGCMUtil.encrypt(attribute);
            }
        } catch (Exception e) {
            logger.error("Failed to encrypt attribute for database storage", e);
            throw new RuntimeException("Failed to encrypt sensitive data", e);
        }
    }
    
    /**
     * 将数据库列值转换为实体属性（解密）
     * 在从数据库查询后调用
     * 
     * @param dbData 数据库中的加密列值
     * @return 解密后的实体属性值
     */
    @Override
    public String convertToEntityAttribute(String dbData) {
        if (!StringUtils.hasText(dbData)) {
            logger.debug("Database data is null or empty, skipping decryption");
            return dbData;
        }
        
        try {
            // 获取当前字段的加密配置
            FieldEncryptionContext context = getCurrentFieldContext();
            
            if (context == null || !context.isEncrypted()) {
                // 非加密字段或无法获取上下文，直接返回原值
                logger.debug("Field is not encrypted or context unavailable, returning original value");
                return dbData;
            }
            
            // 检查是否启用影子字段迁移
            if (context.hasShadowField() && migrationManager != null) {
                // 使用影子字段迁移管理器处理
                logger.debug("Using shadow field migration for decryption");
                return handleShadowFieldDecryption(dbData, context);
            } else {
                // 使用传统的直接解密方式
                logger.debug("Using direct decryption");
                return AESGCMUtil.decrypt(dbData);
            }
        } catch (Exception e) {
            logger.error("Failed to decrypt database data to entity attribute", e);
            throw new RuntimeException("Failed to decrypt sensitive data", e);
        }
    }
    
    /**
     * 处理影子字段加密
     */
    private String handleShadowFieldEncryption(String attribute, FieldEncryptionContext context) {
        EncryptField.MigrationStrategy strategy = context.getMigrationStrategy();
        
        switch (strategy) {
            case DIRECT_ENCRYPT:
                return AESGCMUtil.encrypt(attribute);
                
            case PLAINTEXT_PRIORITY:
                // 明文优先模式，当前字段存储明文，影子字段存储密文
                // 这里返回明文，影子字段的处理由迁移管理器负责
                return attribute;
                
            case SHADOW_PRIORITY:
            case SHADOW_ONLY:
                // 影子字段优先或仅影子字段模式，当前字段不存储数据
                // 实际数据存储在影子字段中
                return null;
                
            default:
                logger.warn("Unknown migration strategy: {}, using direct encryption", strategy);
                return AESGCMUtil.encrypt(attribute);
        }
    }
    
    /**
     * 处理影子字段解密
     */
    private String handleShadowFieldDecryption(String dbData, FieldEncryptionContext context) {
        EncryptField.MigrationStrategy strategy = context.getMigrationStrategy();
        
        switch (strategy) {
            case DIRECT_ENCRYPT:
                return AESGCMUtil.decrypt(dbData);
                
            case PLAINTEXT_PRIORITY:
                // 明文优先模式，当前字段存储的是明文
                return dbData;
                
            case SHADOW_PRIORITY:
            case SHADOW_ONLY:
                // 影子字段优先或仅影子字段模式，当前字段不应该有数据
                // 如果有数据，说明是历史数据，尝试解密
                if (StringUtils.hasText(dbData)) {
                    try {
                        return AESGCMUtil.decrypt(dbData);
                    } catch (Exception e) {
                        logger.warn("Failed to decrypt data in shadow field mode, treating as plaintext: {}", e.getMessage());
                        return dbData;
                    }
                }
                return null;
                
            default:
                logger.warn("Unknown migration strategy: {}, using direct decryption", strategy);
                return AESGCMUtil.decrypt(dbData);
        }
    }
    
    /**
     * 获取当前字段的加密上下文
     * 这是一个简化实现，实际项目中可能需要更复杂的上下文管理
     */
    private FieldEncryptionContext getCurrentFieldContext() {
        // 这里可以通过ThreadLocal、AOP或其他方式获取当前处理的字段信息
        // 为了简化，这里返回一个默认的上下文
        // 实际实现中，应该在实体操作时设置相应的上下文信息
        
        try {
            // 尝试从调用栈中获取字段信息
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            for (StackTraceElement element : stackTrace) {
                String className = element.getClassName();
                String methodName = element.getMethodName();
                
                // 查找实体类的setter/getter方法
                if (methodName.startsWith("set") || methodName.startsWith("get")) {
                    String fieldName = extractFieldNameFromMethod(methodName);
                    if (fieldName != null) {
                        return createFieldContext(className, fieldName);
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to get field context from stack trace: {}", e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 从方法名中提取字段名
     */
    private String extractFieldNameFromMethod(String methodName) {
        if (methodName.startsWith("set") || methodName.startsWith("get")) {
            String fieldName = methodName.substring(3);
            if (fieldName.length() > 0) {
                return Character.toLowerCase(fieldName.charAt(0)) + fieldName.substring(1);
            }
        }
        return null;
    }
    
    /**
     * 创建字段加密上下文
     */
    private FieldEncryptionContext createFieldContext(String className, String fieldName) {
        try {
            Class<?> clazz = Class.forName(className);
            Field field = clazz.getDeclaredField(fieldName);
            EncryptField annotation = field.getAnnotation(EncryptField.class);
            
            if (annotation == null || !annotation.enabled()) {
                return new FieldEncryptionContext(fieldName, false, null, null);
            }
            
            String shadowFieldName = StringUtils.hasText(annotation.shadowField()) 
                ? annotation.shadowField() 
                : fieldName + "_encrypted";
            
            return new FieldEncryptionContext(
                fieldName,
                true,
                shadowFieldName,
                annotation.migrationStrategy()
            );
        } catch (Exception e) {
            logger.debug("Failed to create field context for {}.{}: {}", className, fieldName, e.getMessage());
            return new FieldEncryptionContext(fieldName, false, null, null);
        }
    }
    
    /**
     * 字段加密上下文
     */
    private static class FieldEncryptionContext {
        private final String fieldName;
        private final boolean encrypted;
        private final String shadowFieldName;
        private final EncryptField.MigrationStrategy migrationStrategy;
        
        public FieldEncryptionContext(String fieldName, boolean encrypted, String shadowFieldName, 
                                    EncryptField.MigrationStrategy migrationStrategy) {
            this.fieldName = fieldName;
            this.encrypted = encrypted;
            this.shadowFieldName = shadowFieldName;
            this.migrationStrategy = migrationStrategy;
        }
        
        public String getFieldName() { return fieldName; }
        public boolean isEncrypted() { return encrypted; }
        public String getShadowFieldName() { return shadowFieldName; }
        public EncryptField.MigrationStrategy getMigrationStrategy() { return migrationStrategy; }
        public boolean hasShadowField() { return StringUtils.hasText(shadowFieldName); }
    }
}
