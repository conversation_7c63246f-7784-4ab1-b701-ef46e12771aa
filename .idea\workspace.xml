<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="22add06f-97b4-461a-989f-e9656f93ac4a" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/example/encryption/config/MigrationStrategyConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/example/encryption/config/MigrationStrategyConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/schema.sql" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/schema.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/example/encryption/config/MigrationStrategyConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/example/encryption/config/MigrationStrategyConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/schema.sql" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/schema.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/createdFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/target/maven-status/maven-compiler-plugin/testCompile/default-testCompile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/TEST-com.example.encryption.EncryptionTest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/target/surefire-reports/TEST-com.example.encryption.EncryptionTest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/TEST-com.example.encryption.MybatisEncryptionTest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/target/surefire-reports/TEST-com.example.encryption.MybatisEncryptionTest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/TEST-com.example.encryption.migration.ShadowFieldMigrationTest.xml" beforeDir="false" afterPath="$PROJECT_DIR$/target/surefire-reports/TEST-com.example.encryption.migration.ShadowFieldMigrationTest.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/com.example.encryption.EncryptionTest.txt" beforeDir="false" afterPath="$PROJECT_DIR$/target/surefire-reports/com.example.encryption.EncryptionTest.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/com.example.encryption.MybatisEncryptionTest.txt" beforeDir="false" afterPath="$PROJECT_DIR$/target/surefire-reports/com.example.encryption.MybatisEncryptionTest.txt" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/surefire-reports/com.example.encryption.migration.ShadowFieldMigrationTest.txt" beforeDir="false" afterPath="$PROJECT_DIR$/target/surefire-reports/com.example.encryption.migration.ShadowFieldMigrationTest.txt" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="2ywfJ3PPUo8bsjYHiBMDveLltVl" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "springboot-2.3.12-poc",
    "ignore.virus.scanning.warn.message": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/workspace/DYT/projectCode/hwc/dyt-data-encryption-decryption",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="22add06f-97b4-461a-989f-e9656f93ac4a" name="Changes" comment="" />
      <created>1750750481787</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750750481787</updated>
      <workItem from="1750750483807" duration="484000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>