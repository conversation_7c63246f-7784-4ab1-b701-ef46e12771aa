// 配置axios默认设置
// 使用相对路径，因为前端和后端在同一个服务器上
axios.defaults.baseURL = '';
axios.defaults.timeout = 30000;

// Vue应用实例
new Vue({
    el: '#app',
    data() {
        return {
            // 加载状态
            loading: false,
            strategyLoading: false,
            migrationLoading: false,
            cleanupLoading: false,

            // 自动刷新
            autoRefresh: true,
            refreshTimer: null,

            // 系统状态
            systemStatus: {
                migrationStatus: '未开始',
                cleanupStatus: '未开始',
                overallProgress: 0,
                cleanupProgress: 0,
                encryptedFields: 0,
                activeJobs: 0
            },

            // 当前策略
            currentStrategy: 'PLAINTEXT_PRIORITY',

            // 迁移进度
            migrationProgress: [
                {
                    tableName: 'ych_patient',
                    totalCount: 0,
                    processedCount: 0,
                    progressPercent: 0,
                    status: 'PENDING',
                    lastUpdateTime: null
                },
                {
                    tableName: 'ych_user',
                    totalCount: 0,
                    processedCount: 0,
                    progressPercent: 0,
                    status: 'PENDING',
                    lastUpdateTime: null
                },
                {
                    tableName: 'ych_appoint_record',
                    totalCount: 0,
                    processedCount: 0,
                    progressPercent: 0,
                    status: 'PENDING',
                    lastUpdateTime: null
                }
            ],

            // 清理进度
            cleanupProgress: [
                {
                    tableName: 'ych_patient',
                    totalCount: 0,
                    processedCount: 0,
                    progressPercent: 0,
                    status: 'PENDING',
                    lastProcessedId: null
                },
                {
                    tableName: 'ych_user',
                    totalCount: 0,
                    processedCount: 0,
                    progressPercent: 0,
                    status: 'PENDING',
                    lastProcessedId: null
                },
                {
                    tableName: 'ych_appoint_record',
                    totalCount: 0,
                    processedCount: 0,
                    progressPercent: 0,
                    status: 'PENDING',
                    lastProcessedId: null
                }
            ],

            // 操作日志
            logs: [],
            logIdCounter: 1
        }
    },

    mounted() {
        this.initializeApp();
        this.startAutoRefresh();
    },

    beforeDestroy() {
        this.stopAutoRefresh();
    },

    watch: {
        autoRefresh(newVal) {
            if (newVal) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        }
    },

    methods: {
        // 初始化应用
        async initializeApp() {
            this.addLog('info', '系统初始化中...');
            await this.refreshAll();
            this.addLog('success', '系统初始化完成');
        },

        // 刷新所有数据
        async refreshAll() {
            this.loading = true;
            try {
                await Promise.all([
                    this.getCurrentStrategy(),
                    this.getMigrationProgress(),
                    this.getCleanupProgress(),
                    this.updateSystemStatus()
                ]);
            } catch (error) {
                this.addLog('error', '刷新数据失败: ' + error.message);
            } finally {
                this.loading = false;
            }
        },

        // 获取当前策略
        async getCurrentStrategy() {
            try {
                const response = await axios.get('/api/strategy/config');
                this.currentStrategy = response.data.defaultStrategy || 'PLAINTEXT_PRIORITY';
            } catch (error) {
                this.addLog('warning', '获取当前策略失败，使用默认策略');
                this.currentStrategy = 'PLAINTEXT_PRIORITY';
            }
        },

        // 切换策略
        async switchStrategy(strategy) {
            this.strategyLoading = true;
            try {
                const response = await axios.post('/api/strategy/global', {
                    strategy: strategy,
                    enableOverride: true
                });

                this.currentStrategy = strategy;
                this.addLog('success', `策略已切换到: ${strategy}`);
                this.$message.success('策略切换成功');

                // 刷新系统状态
                await this.updateSystemStatus();
            } catch (error) {
                this.addLog('error', `策略切换失败: ${error.message}`);
                this.$message.error('策略切换失败');
            } finally {
                this.strategyLoading = false;
            }
        },

        // 紧急回滚
        async rollbackStrategy() {
            this.$confirm('确定要执行紧急回滚吗？这将立即回滚到明文优先模式。', '紧急回滚确认', {
                confirmButtonText: '确定回滚',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                this.strategyLoading = true;
                try {
                    await axios.post('/api/strategy/rollback/plaintext');
                    this.currentStrategy = 'PLAINTEXT_PRIORITY';
                    this.addLog('warning', '已执行紧急回滚到明文优先模式');
                    this.$message.success('紧急回滚成功');
                } catch (error) {
                    this.addLog('error', `紧急回滚失败: ${error.message}`);
                    this.$message.error('紧急回滚失败');
                } finally {
                    this.strategyLoading = false;
                }
            });
        },

        // 开始迁移
        async startMigration(version) {
            this.migrationLoading = true;
            try {
                const response = await axios.post(`/api/migration/migrate/${version}`);
                this.addLog('info', `开始迁移到版本 ${version}`);
                this.$message.success('迁移任务已启动');

                // 立即刷新进度
                await this.getMigrationProgress();
            } catch (error) {
                this.addLog('error', `启动迁移失败: ${error.message}`);
                this.$message.error('启动迁移失败');
            } finally {
                this.migrationLoading = false;
            }
        },

        // 验证迁移
        async validateMigration() {
            this.migrationLoading = true;
            try {
                const response = await axios.post('/api/migration/validate');
                this.addLog('success', '迁移验证完成');
                this.$message.success('迁移验证通过');
            } catch (error) {
                this.addLog('error', `迁移验证失败: ${error.message}`);
                this.$message.error('迁移验证失败');
            } finally {
                this.migrationLoading = false;
            }
        },

        // 获取迁移统计
        async getMigrationStats() {
            try {
                const response = await axios.get('/api/migration/stats');
                this.$alert(JSON.stringify(response.data, null, 2), '迁移统计信息', {
                    confirmButtonText: '确定'
                });
            } catch (error) {
                this.addLog('error', `获取迁移统计失败: ${error.message}`);
                this.$message.error('获取迁移统计失败');
            }
        },

        // 获取迁移进度
        async getMigrationProgress() {
            try {
                // 这里模拟获取迁移进度，实际应该调用真实API
                // const response = await axios.get('/api/migration/progress');
                // this.migrationProgress = response.data;

                // 模拟数据更新
                this.migrationProgress.forEach(table => {
                    if (table.status === 'RUNNING') {
                        table.processedCount = Math.min(table.processedCount + Math.floor(Math.random() * 10), table.totalCount);
                        table.progressPercent = table.totalCount > 0 ? (table.processedCount / table.totalCount * 100) : 0;
                        table.lastUpdateTime = new Date().toISOString();
                    }
                });
            } catch (error) {
                this.addLog('warning', '获取迁移进度失败，使用模拟数据');
            }
        },

        // 开始清理
        async startCleanup(tableName) {
            this.cleanupLoading = true;
            try {
                const response = await axios.post('/api/migration/cleanup/start', null, {
                    params: { tableName }
                });

                this.addLog('info', `开始清理表: ${tableName}`);
                this.$message.success(`${tableName} 清理任务已启动`);

                // 更新对应表的状态
                const table = this.cleanupProgress.find(t => t.tableName === tableName);
                if (table) {
                    table.status = 'RUNNING';
                }

                await this.getCleanupProgress();
            } catch (error) {
                this.addLog('error', `启动清理失败: ${error.message}`);
                this.$message.error('启动清理失败');
            } finally {
                this.cleanupLoading = false;
            }
        },

        // 暂停清理
        async pauseCleanup(tableName) {
            try {
                await axios.post('/api/migration/cleanup/pause', null, {
                    params: { tableName }
                });

                this.addLog('warning', `已暂停清理表: ${tableName}`);
                this.$message.success(`${tableName} 清理已暂停`);

                // 更新状态
                const table = this.cleanupProgress.find(t => t.tableName === tableName);
                if (table) {
                    table.status = 'PAUSED';
                }
            } catch (error) {
                this.addLog('error', `暂停清理失败: ${error.message}`);
                this.$message.error('暂停清理失败');
            }
        },

        // 恢复清理
        async resumeCleanup(tableName) {
            try {
                await axios.post('/api/migration/cleanup/resume', null, {
                    params: { tableName }
                });

                this.addLog('info', `已恢复清理表: ${tableName}`);
                this.$message.success(`${tableName} 清理已恢复`);

                // 更新状态
                const table = this.cleanupProgress.find(t => t.tableName === tableName);
                if (table) {
                    table.status = 'RUNNING';
                }
            } catch (error) {
                this.addLog('error', `恢复清理失败: ${error.message}`);
                this.$message.error('恢复清理失败');
            }
        },

        // 暂停所有清理
        async pauseAllCleanup() {
            this.cleanupLoading = true;
            try {
                const tables = ['ych_patient', 'ych_user', 'ych_appoint_record'];
                await Promise.all(tables.map(table => this.pauseCleanup(table)));
                this.addLog('warning', '已暂停所有清理任务');
            } finally {
                this.cleanupLoading = false;
            }
        },

        // 恢复所有清理
        async resumeAllCleanup() {
            this.cleanupLoading = true;
            try {
                const tables = ['ych_patient', 'ych_user', 'ych_appoint_record'];
                await Promise.all(tables.map(table => this.resumeCleanup(table)));
                this.addLog('info', '已恢复所有清理任务');
            } finally {
                this.cleanupLoading = false;
            }
        },

        // 获取清理进度
        async getCleanupProgress() {
            try {
                const response = await axios.get('/api/migration/cleanup/status');

                // 更新清理进度数据
                Object.keys(response.data).forEach(tableName => {
                    const table = this.cleanupProgress.find(t => t.tableName === tableName);
                    if (table) {
                        Object.assign(table, response.data[tableName]);
                    }
                });
            } catch (error) {
                this.addLog('warning', '获取清理进度失败，使用模拟数据');

                // 模拟数据更新
                this.cleanupProgress.forEach(table => {
                    if (table.status === 'RUNNING') {
                        table.processedCount = Math.min(table.processedCount + Math.floor(Math.random() * 5), table.totalCount);
                        table.progressPercent = table.totalCount > 0 ? (table.processedCount / table.totalCount * 100) : 0;
                        table.lastProcessedId = table.processedCount;
                    }
                });
            }
        },

        // 更新系统状态
        async updateSystemStatus() {
            // 计算总体迁移进度
            const totalMigrationRecords = this.migrationProgress.reduce((sum, table) => sum + table.totalCount, 0);
            const processedMigrationRecords = this.migrationProgress.reduce((sum, table) => sum + table.processedCount, 0);
            this.systemStatus.overallProgress = totalMigrationRecords > 0 ? Math.round(processedMigrationRecords / totalMigrationRecords * 100) : 0;

            // 计算总体清理进度
            const totalCleanupRecords = this.cleanupProgress.reduce((sum, table) => sum + table.totalCount, 0);
            const processedCleanupRecords = this.cleanupProgress.reduce((sum, table) => sum + table.processedCount, 0);
            this.systemStatus.cleanupProgress = totalCleanupRecords > 0 ? Math.round(processedCleanupRecords / totalCleanupRecords * 100) : 0;

            // 更新状态
            this.systemStatus.migrationStatus = this.getMigrationStatusText();
            this.systemStatus.cleanupStatus = this.getCleanupStatusText();
            this.systemStatus.encryptedFields = this.getEncryptedFieldsCount();
            this.systemStatus.activeJobs = this.getActiveJobsCount();
        },

        // 获取迁移状态文本
        getMigrationStatusText() {
            const runningTables = this.migrationProgress.filter(t => t.status === 'RUNNING');
            const completedTables = this.migrationProgress.filter(t => t.status === 'COMPLETED');

            if (runningTables.length > 0) return '进行中';
            if (completedTables.length === this.migrationProgress.length) return '已完成';
            return '未开始';
        },

        // 获取清理状态文本
        getCleanupStatusText() {
            const runningTables = this.cleanupProgress.filter(t => t.status === 'RUNNING');
            const completedTables = this.cleanupProgress.filter(t => t.status === 'COMPLETED');

            if (runningTables.length > 0) return '进行中';
            if (completedTables.length === this.cleanupProgress.length) return '已完成';
            return '未开始';
        },

        // 获取加密字段数量
        getEncryptedFieldsCount() {
            // 这里应该从API获取实际的加密字段数量
            return 12; // 模拟数据
        },

        // 获取活跃任务数量
        getActiveJobsCount() {
            const runningMigration = this.migrationProgress.filter(t => t.status === 'RUNNING').length;
            const runningCleanup = this.cleanupProgress.filter(t => t.status === 'RUNNING').length;
            return runningMigration + runningCleanup;
        },

        // 开始自动刷新
        startAutoRefresh() {
            this.stopAutoRefresh();
            this.refreshTimer = setInterval(() => {
                if (this.autoRefresh) {
                    this.refreshAll();
                }
            }, 5000); // 每5秒刷新一次
        },

        // 停止自动刷新
        stopAutoRefresh() {
            if (this.refreshTimer) {
                clearInterval(this.refreshTimer);
                this.refreshTimer = null;
            }
        },

        // 添加日志
        addLog(level, message) {
            const log = {
                id: this.logIdCounter++,
                timestamp: new Date().toLocaleString(),
                level: level,
                message: message
            };

            this.logs.unshift(log);

            // 限制日志数量
            if (this.logs.length > 100) {
                this.logs = this.logs.slice(0, 100);
            }

            // 自动滚动到最新日志
            this.$nextTick(() => {
                if (this.$refs.logContainer) {
                    this.$refs.logContainer.scrollTop = 0;
                }
            });
        },

        // 清空日志
        clearLogs() {
            this.logs = [];
            this.addLog('info', '日志已清空');
        },

        // 获取状态样式类
        getStatusClass(status) {
            switch (status) {
                case '已完成':
                case 'COMPLETED':
                    return 'success';
                case '进行中':
                case 'RUNNING':
                    return 'warning';
                case '失败':
                case 'FAILED':
                    return 'danger';
                default:
                    return '';
            }
        },

        // 获取Alert类型
        getAlertType(strategy) {
            switch (strategy) {
                case 'PLAINTEXT_PRIORITY':
                    return 'info';
                case 'SHADOW_PRIORITY':
                    return 'warning';
                case 'SHADOW_ONLY':
                    return 'success';
                default:
                    return 'info';
            }
        },

        // 获取进度条状态
        getProgressStatus(status) {
            switch (status) {
                case 'COMPLETED':
                    return 'success';
                case 'RUNNING':
                    return null;
                case 'FAILED':
                    return 'exception';
                case 'PAUSED':
                    return 'warning';
                default:
                    return null;
            }
        },

        // 格式化时间
        formatTime(timeStr) {
            if (!timeStr) return '未知';
            return new Date(timeStr).toLocaleString();
        }
    }
});
