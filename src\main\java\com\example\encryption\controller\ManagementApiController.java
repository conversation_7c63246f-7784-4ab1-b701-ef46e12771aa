package com.example.encryption.controller;

import com.example.encryption.config.MigrationStrategyConfig;
import com.example.encryption.annotation.EncryptField.MigrationStrategy;
import com.example.encryption.service.MigrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 管理API控制器
 * 提供前端管理界面所需的API接口
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class ManagementApiController {

    @Autowired
    private MigrationStrategyConfig strategyConfig;

    @Autowired
    private MigrationService migrationService;

    /**
     * 获取策略配置
     */
    @GetMapping("/strategy/config")
    public ResponseEntity<Map<String, Object>> getStrategyConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("defaultStrategy", strategyConfig.getDefaultStrategy().name());
        config.put("enableGlobalOverride", strategyConfig.isEnableGlobalOverride());
        config.put("strategies", strategyConfig.getStrategies());
        return ResponseEntity.ok(config);
    }

    /**
     * 设置全局策略
     */
    @PostMapping("/strategy/global")
    public ResponseEntity<String> setGlobalStrategy(@RequestBody Map<String, Object> request) {
        try {
            String strategyName = (String) request.get("strategy");
            Boolean enableOverride = (Boolean) request.get("enableOverride");

            MigrationStrategy strategy = MigrationStrategy.valueOf(strategyName);
            strategyConfig.setDefaultStrategy(strategy);

            if (enableOverride != null) {
                strategyConfig.setEnableGlobalOverride(enableOverride);
            }

            log.info("全局策略已更新为: {}", strategy);
            return ResponseEntity.ok("策略更新成功");
        } catch (Exception e) {
            log.error("更新全局策略失败", e);
            return ResponseEntity.status(500).body("策略更新失败: " + e.getMessage());
        }
    }

    /**
     * 紧急回滚到明文模式
     */
    @PostMapping("/strategy/rollback/plaintext")
    public ResponseEntity<String> rollbackToPlaintext() {
        try {
            strategyConfig.setDefaultStrategy(MigrationStrategy.PLAINTEXT_PRIORITY);
            strategyConfig.setEnableGlobalOverride(true);

            log.warn("执行紧急回滚到明文优先模式");
            return ResponseEntity.ok("紧急回滚成功");
        } catch (Exception e) {
            log.error("紧急回滚失败", e);
            return ResponseEntity.status(500).body("紧急回滚失败: " + e.getMessage());
        }
    }

    /**
     * 启动迁移到指定版本
     */
    @PostMapping("/migration/migrate/{version}")
    public ResponseEntity<String> startMigration(@PathVariable int version) {
        try {
            MigrationService.MigrationResult result = migrationService.migrateUsersToVersion(version);

            log.info("迁移到版本 {} 完成，成功: {}, 失败: {}", version, result.getSuccessCount(), result.getFailureCount());

            if (result.getFailureCount() == 0) {
                return ResponseEntity.ok("迁移成功完成，处理了 " + result.getSuccessCount() + " 条记录");
            } else {
                return ResponseEntity.ok("迁移部分完成，成功: " + result.getSuccessCount() + ", 失败: " + result.getFailureCount());
            }
        } catch (Exception e) {
            log.error("启动迁移失败", e);
            return ResponseEntity.status(500).body("启动迁移失败: " + e.getMessage());
        }
    }

    /**
     * 验证迁移结果
     */
    @PostMapping("/migration/validate")
    public ResponseEntity<String> validateMigration() {
        try {
            // 这里应该实现实际的验证逻辑
            log.info("开始验证迁移结果");

            // 模拟验证过程
            Thread.sleep(1000);

            log.info("迁移验证完成");
            return ResponseEntity.ok("迁移验证通过");
        } catch (Exception e) {
            log.error("迁移验证失败", e);
            return ResponseEntity.status(500).body("迁移验证失败: " + e.getMessage());
        }
    }

    /**
     * 获取迁移统计信息
     */
    @GetMapping("/migration/stats")
    public ResponseEntity<MigrationService.MigrationStatistics> getMigrationStats() {
        try {
            MigrationService.MigrationStatistics stats = migrationService.getMigrationStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取迁移统计失败", e);
            return ResponseEntity.status(500).body(null);
        }
    }

    /**
     * 启动清理任务
     */
    @PostMapping("/migration/cleanup/start")
    public ResponseEntity<String> startCleanup(@RequestParam String tableName) {
        try {
            log.info("启动清理任务: {}", tableName);

            // 这里应该调用实际的清理服务
            // cleanupService.startCleanup(tableName);

            return ResponseEntity.ok("清理任务已启动: " + tableName);
        } catch (Exception e) {
            log.error("启动清理任务失败", e);
            return ResponseEntity.status(500).body("启动清理任务失败: " + e.getMessage());
        }
    }

    /**
     * 暂停清理任务
     */
    @PostMapping("/migration/cleanup/pause")
    public ResponseEntity<String> pauseCleanup(@RequestParam String tableName) {
        try {
            log.info("暂停清理任务: {}", tableName);

            // 这里应该调用实际的清理服务
            // cleanupService.pauseCleanup(tableName);

            return ResponseEntity.ok("清理任务已暂停: " + tableName);
        } catch (Exception e) {
            log.error("暂停清理任务失败", e);
            return ResponseEntity.status(500).body("暂停清理任务失败: " + e.getMessage());
        }
    }

    /**
     * 恢复清理任务
     */
    @PostMapping("/migration/cleanup/resume")
    public ResponseEntity<String> resumeCleanup(@RequestParam String tableName) {
        try {
            log.info("恢复清理任务: {}", tableName);

            // 这里应该调用实际的清理服务
            // cleanupService.resumeCleanup(tableName);

            return ResponseEntity.ok("清理任务已恢复: " + tableName);
        } catch (Exception e) {
            log.error("恢复清理任务失败", e);
            return ResponseEntity.status(500).body("恢复清理任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取清理状态
     */
    @GetMapping("/migration/cleanup/status")
    public ResponseEntity<Map<String, Object>> getCleanupStatus() {
        try {
            Map<String, Object> status = new HashMap<>();

            // 模拟清理状态数据
            Map<String, Object> patientStatus = new HashMap<>();
            patientStatus.put("tableName", "ych_patient");
            patientStatus.put("totalCount", 1000L);
            patientStatus.put("processedCount", 650L);
            patientStatus.put("progressPercent", 65.0);
            patientStatus.put("status", "RUNNING");
            patientStatus.put("lastProcessedId", 650L);

            Map<String, Object> userStatus = new HashMap<>();
            userStatus.put("tableName", "ych_user");
            userStatus.put("totalCount", 500L);
            userStatus.put("processedCount", 200L);
            userStatus.put("progressPercent", 40.0);
            userStatus.put("status", "PAUSED");
            userStatus.put("lastProcessedId", 200L);

            Map<String, Object> appointStatus = new HashMap<>();
            appointStatus.put("tableName", "ych_appoint_record");
            appointStatus.put("totalCount", 2000L);
            appointStatus.put("processedCount", 2000L);
            appointStatus.put("progressPercent", 100.0);
            appointStatus.put("status", "COMPLETED");
            appointStatus.put("lastProcessedId", 2000L);

            status.put("ych_patient", patientStatus);
            status.put("ych_user", userStatus);
            status.put("ych_appoint_record", appointStatus);

            return ResponseEntity.ok(status);
        } catch (Exception e) {
            log.error("获取清理状态失败", e);
            return ResponseEntity.status(500).body(null);
        }
    }
}
